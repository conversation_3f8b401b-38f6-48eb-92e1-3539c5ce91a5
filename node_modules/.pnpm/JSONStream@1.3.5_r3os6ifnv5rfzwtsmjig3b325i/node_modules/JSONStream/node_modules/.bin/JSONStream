#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/sites/tavistock/wp-content/themes/tavi-main/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="$NODE_PATH:/Users/<USER>/sites/tavistock/wp-content/themes/tavi-main/node_modules/.pnpm/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin.js" "$@"
else
  exec node  "$basedir/../../bin.js" "$@"
fi
