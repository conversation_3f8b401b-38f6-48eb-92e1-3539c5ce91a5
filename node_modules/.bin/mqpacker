#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/sites/sentencing-toolkit/wp-content/themes/sentencing-toolkit/node_modules/.pnpm/css-mqpacker@7.0.0/node_modules/css-mqpacker/bin/node_modules:/Users/<USER>/sites/sentencing-toolkit/wp-content/themes/sentencing-toolkit/node_modules/.pnpm/css-mqpacker@7.0.0/node_modules/css-mqpacker/node_modules:/Users/<USER>/sites/sentencing-toolkit/wp-content/themes/sentencing-toolkit/node_modules/.pnpm/css-mqpacker@7.0.0/node_modules:/Users/<USER>/sites/sentencing-toolkit/wp-content/themes/sentencing-toolkit/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/sites/sentencing-toolkit/wp-content/themes/sentencing-toolkit/node_modules/.pnpm/css-mqpacker@7.0.0/node_modules/css-mqpacker/bin/node_modules:/Users/<USER>/sites/sentencing-toolkit/wp-content/themes/sentencing-toolkit/node_modules/.pnpm/css-mqpacker@7.0.0/node_modules/css-mqpacker/node_modules:/Users/<USER>/sites/sentencing-toolkit/wp-content/themes/sentencing-toolkit/node_modules/.pnpm/css-mqpacker@7.0.0/node_modules:/Users/<USER>/sites/sentencing-toolkit/wp-content/themes/sentencing-toolkit/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../css-mqpacker/bin/mqpacker.js" "$@"
else
  exec node  "$basedir/../css-mqpacker/bin/mqpacker.js" "$@"
fi
