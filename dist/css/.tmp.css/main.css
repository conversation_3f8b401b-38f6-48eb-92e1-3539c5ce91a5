@charset "UTF-8";
/*! modern-normalize v2.0.0 | MIT License | https://github.com/sindresorhus/modern-normalize */
/*
Document
========
*/
/**
Use a better box model (opinionated).
*/
*,
::before,
::after {
  box-sizing: border-box;
}

html {
  /* Improve consistency of default fonts in all browsers. (https://github.com/sindresorhus/modern-normalize/issues/3) */
  font-family: system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  line-height: 1.15; /* 1. Correct the line height in all browsers. */
  -webkit-text-size-adjust: 100%; /* 2. Prevent adjustments of font size after orientation changes in iOS. */
  -moz-tab-size: 4; /* 3. Use a more readable tab size (opinionated). */
  tab-size: 4; /* 3 */
}

/*
Sections
========
*/
body {
  margin: 0; /* Remove the margin in all browsers. */
}

/*
Grouping content
================
*/
/**
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
*/
hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
}

/*
Text-level semantics
====================
*/
/**
Add the correct text decoration in Chrome, Edge, and Safari.
*/
abbr[title] {
  text-decoration: underline dotted;
}

/**
Add the correct font weight in Edge and Safari.
*/
b,
strong {
  font-weight: bolder;
}

/**
1. Improve consistency of default fonts in all browsers. (https://github.com/sindresorhus/modern-normalize/issues/3)
2. Correct the odd 'em' font sizing in all browsers.
*/
code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/**
Add the correct font size in all browsers.
*/
small {
  font-size: 80%;
}

/**
Prevent 'sub' and 'sup' elements from affecting the line height in all browsers.
*/
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
Tabular data
============
*/
/**
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
*/
table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
}

/*
Forms
=====
*/
/**
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
*/
button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-size: 100%; /* 1 */
  line-height: 1.15; /* 1 */
  margin: 0; /* 2 */
}

/**
Remove the inheritance of text transform in Edge and Firefox.
*/
button,
select {
  text-transform: none;
}

/**
Correct the inability to style clickable types in iOS and Safari.
*/
button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}

/**
Remove the inner border and padding in Firefox.
*/
::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
Restore the focus styles unset by the previous rule.
*/
:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
Remove the additional ':invalid' styles in Firefox.
See: https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737
*/
:-moz-ui-invalid {
  box-shadow: none;
}

/**
Remove the padding so developers are not caught out when they zero out 'fieldset' elements in all browsers.
*/
legend {
  padding: 0;
}

/**
Add the correct vertical alignment in Chrome and Firefox.
*/
progress {
  vertical-align: baseline;
}

/**
Correct the cursor style of increment and decrement buttons in Safari.
*/
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/**
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/
[type=search] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/**
Remove the inner padding in Chrome and Safari on macOS.
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to 'inherit' in Safari.
*/
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Interactive
===========
*/
/*
Add the correct display in Chrome and Safari.
*/
summary {
  display: list-item;
}

/* latin-ext */
@font-face {
  font-family: "Montserrat";
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url(../fonts/JTUQjIg1_i6t8kCHKm459WxRxy7m0dR9pBOi.woff2) format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Montserrat";
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url(../fonts/JTUQjIg1_i6t8kCHKm459WxRyS7m0dR9pA.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "Montserrat";
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(../fonts/JTUSjIg1_i6t8kCHKm459WdhyyTh89ZNpQ.woff2) format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Montserrat";
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(../fonts/JTUSjIg1_i6t8kCHKm459WlhyyTh89Y.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "Noticia Text";
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url(../fonts/VuJodNDF2Yv9qppOePKYRP12YwtUn07_pjjsQdA.woff2) format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Noticia Text";
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url(../fonts/VuJodNDF2Yv9qppOePKYRP12Ywtan07_pjjs.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "Noticia Text";
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url(../fonts/VuJrdNDF2Yv9qppOePKYRP12YwPhulvchDXGe9nyfeU.woff2) format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Noticia Text";
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url(../fonts/VuJrdNDF2Yv9qppOePKYRP12YwPhulvShDXGe9ny.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "Noticia Text";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(../fonts/VuJ2dNDF2Yv9qppOePKYRP12aDtYlUndpAjt.woff2) format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Noticia Text";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(../fonts/VuJ2dNDF2Yv9qppOePKYRP12ZjtYlUndpA.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "Noticia Text";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(../fonts/VuJpdNDF2Yv9qppOePKYRP1-3R5NtmvQjjLkeenz.woff2) format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Noticia Text";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(../fonts/VuJpdNDF2Yv9qppOePKYRP1-3R5NuGvQjjLkeQ.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
.container {
  margin-left: auto;
  margin-right: auto;
  width: calc(100% - var(--side-gutter) * 2);
}

@media (max-width: 600px) {
  .hidden-accessible-mob {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
    word-wrap: normal !important;
  }
}
.custom-scrollbar {
  --scrollbarBG: #fff;
  --thumbBG: colours("primary");
  padding-right: 2px;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar {
  --scrollbarBG: #fff;
  --thumbBG: colours("primary");
}
.custom-scrollbar::-webkit-scrollbar {
  -webkit-appearance: none;
  background-color: var(--scrollbarBG);
  height: 4px;
  width: 4px;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: var(--thumbBG);
}

:root {
  --side-gutter: 1.125rem;
}
@media (min-width: 1024px) {
  :root {
    --side-gutter: min(120px, 5vw);
  }
}

:root {
  --header-height: 58px;
}
@media (min-width: 869px) {
  :root {
    --header-height: 142px;
  }
}

html,
body {
  --wp--preset--font-size--large: 72px;
  min-height: 100%;
}

body {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
  background: var(--bg-colour, rgb(246.3, 250.6, 249.6));
  color: #1a1a1a;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  overflow-y: scroll;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.primary-content {
  flex: 1 0 auto;
}

body.mce-content-body {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
}

.anchor-line {
  right: 200vw;
  position: absolute;
  top: 0;
  width: 12px;
  z-index: 1;
}
.anchor-line__section {
  position: absolute;
  width: 100%;
}

.has-heading-1-xl-font-size {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 72px;
  line-height: 80px;
  letter-spacing: -0.01em;
}
@media (max-width: 600px) {
  .has-heading-1-xl-font-size {
    font-weight: 500;
    font-size: 40px;
    letter-spacing: 0;
    line-height: 44px;
  }
}

h1,
.has-heading-1-font-size {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 56px;
  line-height: 66px;
  letter-spacing: -0.01em;
}
@media (max-width: 600px) {
  h1,
  .has-heading-1-font-size {
    font-size: 36px;
    letter-spacing: 0;
    line-height: 42px;
  }
}

h2,
.has-heading-2-font-size {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 42px;
  line-height: 52px;
  letter-spacing: -0.01em;
}
@media (max-width: 600px) {
  h2,
  .has-heading-2-font-size {
    font-weight: 700;
    font-size: 28px;
    line-height: 34px;
  }
}

h3,
.has-heading-3-font-size {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 32px;
  letter-spacing: 0;
  line-height: 40px;
}
@media (max-width: 600px) {
  h3,
  .has-heading-3-font-size {
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    letter-spacing: 0;
    line-height: 30px;
  }
}

h4,
.has-heading-4-font-size {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 24px;
  letter-spacing: 0;
  line-height: 28px;
}

h5,
.has-heading-5-font-size {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  letter-spacing: 0;
  line-height: 25px;
}

h6,
.has-heading-6-font-size {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
}

.has-body-copy-font-size {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
}

.has-small-font-size {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
}

:where(img) {
  height: auto;
  max-width: 100%;
}

:where(a) {
  color: currentColor;
  text-decoration: underline;
  text-underline-offset: 3px;
  text-decoration-thickness: 1px;
  transition: all 0.1s;
  word-break: break-word;
}
:where(a):hover, :where(a):focus {
  text-decoration-thickness: 2px;
}

b,
strong {
  font-weight: 500;
}

:is(ol, ul) {
  padding-left: 0.75rem;
}
:is(ol, ul).has-small-font-size {
  padding-left: 0;
}
:is(ol, ul) > li {
  margin-top: 0;
}
:is(ol, ul) > li:where(:not(:last-child)) {
  margin-bottom: 0.75rem;
}
:is(ol, ul) > li > :is(ol, ul) {
  margin: 0.5rem 0;
}

.fine-print {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
  background: rgb(164.2, 186.2, 195.4);
  border-radius: 8px;
  clear: both;
  padding: 0.5rem 0.75rem;
}

h1:is(h1) + :is(.page-subtitle) {
  color: #193b39;
  margin-top: -1.125rem;
}

.count-up {
  font-variant-numeric: lining-nums;
}

a.anchorlink {
  font-size: 14px;
  position: absolute;
}

/* Text meant only for screen readers. */
.sr-only,
.screen-reader-text,
.screen-reader-only {
  clip: rect(1px, 1px, 1px, 1px);
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
  word-wrap: normal !important;
}

.sr-only:focus {
  background: #f1f1f1;
  border-radius: 3px;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
  clip: auto !important;
  color: #1a1a1a;
  display: block;
  font-size: 14px;
  font-size: 0.875rem;
  font-weight: bold;
  height: auto;
  left: 5px;
  line-height: normal;
  padding: 15px 23px 14px;
  text-decoration: none;
  top: 5px;
  width: auto;
  z-index: 100000;
  /* Above WP toolbar. */
}

/* Do not show the outline on the skip link target. */
#primary[tabindex="-1"]:focus {
  outline: 0;
}

.js-skip-link:focus-within {
  background-color: #f1f1f1;
  border-radius: 3px;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
  clip: auto !important;
  color: #1a1a1a;
  display: block;
  font-size: 14px;
  font-size: 0.875rem;
  font-weight: bold;
  height: auto;
  left: 5px;
  line-height: normal;
  padding: 15px 23px 14px;
  text-decoration: none;
  top: 5px;
  width: auto;
  z-index: 100000;
}

:is(.styled-checkbox, .styled-radio, .styled-switch) > input:focus-visible + span:before {
  box-shadow: inset 0px 0px 0 1px currentColor;
  outline: 4px solid #f6be46 !important;
}

a:focus-visible {
  background: #f6be46;
  box-shadow: 0 -2px #f6be46, 0 4px #1a1a1a;
  color: #1a1a1a !important;
  outline: 4px solid rgba(0, 0, 0, 0);
  text-decoration: none !important;
}

:is(.main-logo, .wp-block-image a) {
  display: block;
}
:is(.main-logo, .wp-block-image a):focus-visible {
  background: #f6be46;
  box-shadow: 0 0 0 7px #f6be46;
  outline: 3px solid #000;
}
:is(.main-logo, .wp-block-image a) > img {
  display: block;
}

.wp-block-image a:hover {
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.5333333333)) drop-shadow(0 0 4px rgba(0, 0, 0, 0.5333333333)) drop-shadow(0 0 17px rgba(0, 0, 0, 0.2));
}

.highlight-transition {
  box-shadow: 0;
  transition: all 0.6s ease-in-out;
}

.highlight.highlight-transition {
  box-shadow: 4px 4px #1c536a, -4px -4px #1c536a, -4px 4px #1c536a, 4px -4px #1c536a;
  transition: all 0.2s ease-in-out;
}

:root {
  --nav-text-color: #000;
}

.main-header {
  background: var(--bg-colour, rgb(246.3, 250.6, 249.6));
  border-bottom: solid 1px #d7d7d7;
  padding: 0;
  position: relative;
  top: 0;
  transition: background 0.4s ease-out 0.1s, color 0.2s ease-out 0.2s;
  z-index: 200;
}
.main-header .container {
  align-items: center;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.main-header .main-logo,
.main-header .main-nav a {
  color: var(--nav-text-color);
}

.navs-wrap {
  flex: 1 0 100%;
}

.top-nav--desktop {
  display: flex;
  flex: 1 0 auto;
  justify-content: flex-end;
}

@media (max-width: 868px) {
  .top-nav--desktop {
    display: none !important;
  }
}
@media (min-width: 869px) {
  .top-nav--mobile {
    display: none !important;
  }
}
.main-logo {
  padding: 1.5rem 0;
}

:is(.main-nav, .top-nav) {
  display: flex;
  list-style: none;
  gap: 0.5rem;
  margin: 0;
  padding: 0;
}
@media (min-width: 1221px) {
  :is(.main-nav, .top-nav) {
    gap: 1.5rem;
  }
}
:is(.main-nav, .top-nav) > li {
  margin: 0;
  padding: 0;
}
:is(.main-nav, .top-nav) > li:first-child {
  margin-left: -8px;
}
:is(.main-nav, .top-nav) > li > a {
  color: #000;
  display: block;
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 16px;
  padding: 22px 8px;
  position: relative;
  text-decoration: none;
}
:is(.main-nav, .top-nav) > li > a:before {
  background: currentColor;
  bottom: 20px;
  content: "";
  height: 1px;
  left: 8px;
  opacity: 0;
  position: absolute;
  transition: opacity 0.2s ease-out;
  right: 8px;
}
:is(.main-nav, .top-nav) > li.current_page_item > a:before,
:is(.main-nav, .top-nav) > li > a:hover:before,
:is(.main-nav, .top-nav) > li > a[aria-expanded=true]:before {
  opacity: 1;
}
:is(.main-nav, .top-nav) > li > a[aria-haspopup=true] {
  margin-right: 28px;
}
:is(.main-nav, .top-nav) > li > a[aria-haspopup=true] .expanded-status {
  position: absolute;
  left: 100%;
  top: calc(50% - 3px);
  transition: transform 0.2s ease-out;
}
:is(.main-nav, .top-nav) > li > a[aria-expanded=true] {
  color: #1c536a;
}
:is(.main-nav, .top-nav) > li > a[aria-expanded=true] .expanded-status {
  transform: rotate(180deg);
}

:is(.main-nav, .top-nav--desktop .top-nav) > li.button > a {
  align-items: flex-start;
  background: var(--background, rgb(73.4, 117.4, 135.8));
  border-radius: 0;
  border: 0;
  color: #fff;
  cursor: pointer;
  display: inline-flex;
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 18px;
  padding: 0.75rem 2rem;
  text-align: center;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
}
:is(.main-nav, .top-nav--desktop .top-nav) > li.button > a:hover, :is(.main-nav, .top-nav--desktop .top-nav) > li.button > a:hover:visited, :is(.main-nav, .top-nav--desktop .top-nav) > li.button > a:focus {
  background: var(--background-hover, #1c536a);
  outline: none;
  text-decoration: underline;
}
:is(.main-nav, .top-nav--desktop .top-nav) > li.button > a:focus-visible {
  box-shadow: inset 0px 0px 0 2px #000;
  outline: 4px solid #f6be46 !important;
  outline-offset: 0;
}
:is(.main-nav, .top-nav--desktop .top-nav) > li.button > a::before {
  display: none;
}

.main-nav-toggle {
  background: none;
  border: 0;
  color: currentColor;
  cursor: pointer;
  display: none;
  margin: 0;
  padding: 0;
  position: absolute;
  right: 0.75rem;
  top: calc(var(--top-nav-height) + var(--header-height) / 2 - 20px);
}
body.admin-bar .main-nav-toggle {
  top: calc(var(--wp-admin--admin-bar--height) + var(--top-nav-height) + var(--header-height) / 2 - 20px);
}
.main-nav-toggle .icon {
  display: block;
  height: 40px;
  position: relative;
  width: 40px;
}
.main-nav-toggle .icon > span {
  background: currentColor;
  border-radius: 2px;
  height: 4px;
  left: calc(50% - 20px);
  position: absolute;
  top: calc(50% - 2px);
  transition: all 0.2s ease-out;
  transform-origin: center;
  width: 100%;
}
.main-nav-toggle .icon > span:first-child {
  transform: translateY(-12px);
}
.main-nav-toggle .icon > span:last-child {
  transform: translateY(12px);
}
.main-nav-toggle[aria-expanded=true] .icon > span:first-child {
  transform: rotate(45deg) translateY(0);
}
.main-nav-toggle[aria-expanded=true] .icon > span:nth-of-type(2) {
  opacity: 0;
  background: transparent;
}
.main-nav-toggle[aria-expanded=true] .icon > span:last-child {
  transform: rotate(-45deg) translateY(0);
}

@media (max-width: 868px) {
  .main-nav-toggle {
    display: block;
  }
  .header-search {
    align-items: center;
    padding: 22px 8px;
  }
  .header-search .header-search__input {
    max-width: none;
  }
}
.main-nav > li > .menu-item-search {
  border: solid 1px transparent;
  height: 40px;
  padding: 0;
  width: 40px;
}
.main-nav > li > .menu-item-search:hover, .main-nav > li > .menu-item-search:focus-visible {
  background-color: rgb(232.3, 237.8, 240.1);
  color: #1a1a1a;
}
.main-nav > li > .menu-item-search[aria-expanded=true] {
  border-color: #000;
}
.main-nav > li > .menu-item-search::before {
  mask: url(../images/search.svg) no-repeat 13px 12px;
  mask-size: 14px 13px;
  background: currentColor;
  height: 100%;
  inset: 0;
  opacity: 1;
  width: 100%;
}
.main-nav > li > .menu-item-search .expanded-status {
  display: none !important;
}

.header-search {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin: 0 auto;
}
.header-search__input {
  border: solid 1px #000;
  height: 42px;
  max-width: 252px;
}
.header-search__submit {
  align-items: flex-start;
  background: var(--background, rgb(73.4, 117.4, 135.8));
  border-radius: 0;
  border: 0;
  color: #fff;
  cursor: pointer;
  display: inline-flex;
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 18px;
  padding: 0.75rem 2rem;
  text-align: center;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
  align-items: center;
  min-height: 42px;
  padding: 9px 1.125rem;
}
.header-search__submit:hover, .header-search__submit:hover:visited, .header-search__submit:focus {
  background: var(--background-hover, #1c536a);
  outline: none;
  text-decoration: underline;
}
.header-search__submit:focus-visible {
  box-shadow: inset 0px 0px 0 2px #000;
  outline: 4px solid #f6be46 !important;
  outline-offset: 0;
}

@media (max-width: 868px) {
  .main-header {
    position: static;
  }
  .main-logo {
    padding: 0.75rem 0;
  }
  .navs-wrap {
    --nav-text-color: #000;
    align-items: stretch;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    inset: calc(var(--top-nav-height) + var(--header-height)) 0 0 0;
    overflow: auto;
    position: absolute;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease-out, visibility 0.2s ease-out;
  }
  body.admin-bar .navs-wrap {
    top: calc(var(--top-nav-height) + var(--wp-admin--admin-bar--height) + var(--header-height));
  }
  .navs-wrap > * {
    opacity: 0;
    transition: opacity 0.2s ease-out;
  }
  :is(.main-nav, .top-nav--mobile .top-nav) {
    display: none;
    gap: 0;
    flex-direction: column;
    list-style: none;
  }
  .expanded :is(.main-nav, .top-nav--mobile .top-nav) {
    display: flex;
  }
  :is(.main-nav, .top-nav--mobile .top-nav) > li {
    border-bottom: #cccccc solid 1px;
    margin: 0;
  }
  :is(.main-nav, .top-nav--mobile .top-nav) > li > a::before {
    display: none;
  }
  .sub-menu.sub-menu {
    --shadow-color: rgba(156, 186, 201, 0.2);
    box-shadow: inset 0 1px 2px var(--shadow-color), inset 0 2px 4px var(--shadow-color), inset 0 4px 8px var(--shadow-color);
    container-type: inline-size;
    margin: 0 auto;
    max-height: 0;
    padding: 0;
    position: relative;
    opacity: 1;
    visibility: visible;
    overflow: clip;
    transition: opacity 0.1s ease-out, height 2s ease-out;
  }
  .sub-menu.sub-menu > * {
    opacity: 0;
    transition: opacity 0.2s ease-out;
  }
  .sub-menu.sub-menu > *:first-child {
    margin-top: 1.5rem;
  }
  .sub-menu.sub-menu > *:last-child {
    margin-bottom: 40px;
  }
  .sub-menu.sub-menu[aria-hidden=false] {
    max-height: none;
    opacity: 1;
    overflow: auto;
  }
  .sub-menu.sub-menu[aria-hidden=false] > * {
    opacity: 1;
    transition: opacity 0.4s 0.3s ease-out;
  }
  .sub-menu.sub-menu.post-type-sub-menus--search {
    box-shadow: none;
    padding: 0;
    clip: auto;
    margin: 0 !important;
    max-height: none;
    padding: 1.5rem 0.5rem;
    opacity: 1;
    overflow: auto;
    visibility: visible;
  }
  .sub-menu.sub-menu.post-type-sub-menus--search > * {
    opacity: 1;
  }
  .main-header.expanded {
    --nav-text-color: #1a1a1a !important;
    --logo-color:  !important;
    background: #fff;
  }
  .main-header.expanded .navs-wrap {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.2s 0.2s ease-out, visibility 0.2s 0.2s ease-out;
  }
  .main-header.expanded .navs-wrap > * {
    opacity: 1;
    transition: opacity 0.6s 0.4s ease-out;
  }
}
:root {
  --top-nav-height: 48px;
}

.top-bar {
  background: #000;
  color: #fff;
  font-family: Montserrat;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  padding: 0.75rem 1.125rem;
}

:where(.wp-block-cover-image:not(.has-text-color)),
:where(.wp-block-cover:not(.has-text-color)) {
  color: #1a1a1a;
}

.wp-block-cover .wp-block-heading:where(:not(.has-text-color)) a {
  color: currentColor;
}

:root {
  --gap: 40px;
  --wp--style--gallery-gap-default: 40px;
  --gallery-block--gutter-size: 40px;
  --wp--style--block-gap: 40px;
}

:where(.is-layout-grid) {
  gap: var(--gap);
}

:where(.is-layout-flex) {
  gap: 40px;
}

.wp-block-cover .wp-block-cover__inner-container,
.wp-block-cover-image .wp-block-cover__inner-container {
  color: unset;
}

.wp-block-image {
  margin-bottom: 0;
  margin-top: 0;
}

:where(p, ol, ul) {
  margin-bottom: 1.5rem;
  margin-top: 1.5rem;
}

:where(h1, h2, h3, h4, h5, h6, .wp-block-buttons, .wp-block-columns, .wp-block-cover.aligncenter, .wp-block-group, .wp-block-quote, .wp-block-pullquote, .cards-container, iframe, .wp-block-group-is-layout-grid) {
  margin-bottom: 2.5rem;
  margin-top: 2.5rem;
}

:where(p, h1, h2, h3, h4, h5, h6, ul, ol, .wp-block-buttons, .wp-block-columns, .wp-block-group, .wp-block-quote, .wp-block-pullquote, .cards-container, iframe, .wp-block-group-is-layout-grid).has-small-font-size {
  margin-bottom: 0.75rem;
  margin-top: 0.75rem;
}
:where(p, h1, h2, h3, h4, h5, h6, ul, ol, .wp-block-buttons, .wp-block-columns, .wp-block-group, .wp-block-quote, .wp-block-pullquote, .cards-container, iframe, .wp-block-group-is-layout-grid):first-child {
  margin-top: 0;
}
:where(p, h1, h2, h3, h4, h5, h6, ul, ol, .wp-block-buttons, .wp-block-columns, .wp-block-group, .wp-block-quote, .wp-block-pullquote, .cards-container, iframe, .wp-block-group-is-layout-grid):last-child {
  margin-bottom: 0;
}

:where(p, h1, h2, h3, h4, h5, h6, ul, ol, .wp-block-buttons) img {
  display: inline-block;
  margin-right: 0.75rem;
  vertical-align: middle;
}

p:has(+ :where(ol, ul)) {
  margin-bottom: 0.5rem;
}

p + :where(ol, ul) {
  margin-top: 0.5rem;
}

.wp-block-audio audio {
  display: block;
}

:where(.cards, .listing, .wp-block-video) {
  margin-bottom: 3rem;
  margin-top: 3rem;
}

:where(p, .wp-block-quote, .has-media-on-the-right .wp-block-media-text__media, .has-media-on-the-left .wp-block-media-text__media, .featured-view, ul, ol) + :where(.wp-block-cover, h1, h2, h3, h4, h5, h6, .has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size) {
  margin-top: 2.5rem;
}

:is(h1, h2, h3, h4, h5, h6, .has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size) + :is(.cards-container, .listing, .cards) {
  margin-top: 1.5rem;
}

:where(h1, h2, h3, h4, h5, h6, .has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size) {
  margin-bottom: 0.5rem;
}
:where(h1, h2, h3, h4, h5, h6, .has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size) + :where(p:not(.has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size), ol, ul) {
  margin-top: 0.75rem;
}
:where(h1, h2, h3, h4, h5, h6, .has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size) + :where(h1, h2, h3, h4, h5, h6, .has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size, .cards-container, .listing, .cards) {
  margin-top: 0.75rem;
}

.wp-block-cover + :is(p, h1, h2, h3, h4, h5, h6, .has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size, .listing, .cards-container) {
  margin-top: 2.5rem;
}

.wp-block-media-text.wp-block-media-text > .wp-block-media-text__content {
  max-width: 560px;
  margin: 0 1.5rem auto auto;
  padding: 3rem 1.125rem;
  width: 100%;
}

@media (min-width: 768px) {
  .wp-block-media-text.wp-block-media-text.alignfull > .wp-block-media-text__content {
    margin: 0 5rem auto auto;
  }
}
.wp-block-media-text.wp-block-media-text {
  min-height: 400px;
}
.wp-block-media-text.wp-block-media-text.alignfull {
  min-height: 615px;
}

:is(.entry-content, .site-main) > :is(:not(.wp-block-cover:not(.aligncenter))):last-child {
  margin-bottom: 3rem;
}

.is-style-underlined {
  border-bottom: solid 1px currentColor;
  margin-bottom: 1.5rem;
  padding-bottom: 4px;
}
.is-style-underlined a {
  text-decoration: none;
}

.has-body-large-font-size {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 36px;
  letter-spacing: -0.01em;
}

.has-body-xl-font-size {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 36px;
  letter-spacing: -0.01em;
}

.has-small-copy-font-size {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
}

.has-x-small-font-size {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
}

.has-text-align-center {
  text-align: center;
}

.has-text-align-right {
  text-align: right;
}

.wp-block-quote,
.wp-block-quote.is-style-large,
.wp-block-quote.has-text-align-right {
  font-style: normal;
  font-weight: 300;
  font-size: 28px;
  line-height: 36px;
  border: 0;
  letter-spacing: 0.02em;
  margin: 2.5rem auto;
  padding: 0;
}
.wp-block-quote p:last-of-type,
.wp-block-quote.is-style-large p:last-of-type,
.wp-block-quote.has-text-align-right p:last-of-type {
  margin-bottom: 0;
}
.wp-block-quote cite,
.wp-block-quote.is-style-large cite,
.wp-block-quote.has-text-align-right cite {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
  display: block;
  margin: 12px 0 0;
  text-transform: none;
}
.wp-block-quote cite::before,
.wp-block-quote.is-style-large cite::before,
.wp-block-quote.has-text-align-right cite::before {
  content: "— ";
}

.wp-block-pullquote,
.wp-block-pullquote p {
  font-style: normal;
  font-weight: 300;
  font-size: 28px;
  line-height: 36px;
  text-align: left;
}
.wp-block-pullquote .wp-block-pullquote__citation,
.wp-block-pullquote cite,
.wp-block-pullquote p .wp-block-pullquote__citation,
.wp-block-pullquote p cite {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 24px;
  letter-spacing: 0;
  line-height: 28px;
  margin: 15px 0 0;
  text-transform: none;
}

.wp-block-buttons {
  display: flex;
  gap: 1.125rem;
}
.wp-block-buttons .wp-block-button {
  margin: 0;
}

.wp-block-buttons.is-vertical .wp-block-button {
  margin: 0;
}

.wp-block-button__link,
.wp-block-file .wp-block-file__button {
  align-items: flex-start;
  background: var(--background, rgb(73.4, 117.4, 135.8));
  border-radius: 0;
  border: 0;
  color: #fff;
  cursor: pointer;
  display: inline-flex;
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 18px;
  padding: 0.75rem 2rem;
  text-align: center;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
}
.wp-block-button__link:hover, .wp-block-button__link:hover:visited, .wp-block-button__link:focus,
.wp-block-file .wp-block-file__button:hover,
.wp-block-file .wp-block-file__button:hover:visited,
.wp-block-file .wp-block-file__button:focus {
  background: var(--background-hover, #1c536a);
  outline: none;
  text-decoration: underline;
}
.wp-block-button__link:focus-visible,
.wp-block-file .wp-block-file__button:focus-visible {
  box-shadow: inset 0px 0px 0 2px #000;
  outline: 4px solid #f6be46 !important;
  outline-offset: 0;
}

.is-style-small-button .wp-block-button__link {
  padding: 7px 12px 5px;
}

.is-style-download .wp-block-button__link {
  align-items: center;
  display: flex;
  gap: 12px;
}
.is-style-download .wp-block-button__link:before {
  background: currentColor;
  mask-image: url(../images/download.svg);
  mask-repeat: no-repeat;
  content: "";
  display: block;
  flex: 0 0 19px;
  height: 21px;
  position: relative;
  transition: transform 0.1s ease-in;
  width: 19px;
}

.is-style-arrow .wp-block-button__link {
  align-items: center;
  display: flex;
  gap: 12px;
}
.is-style-arrow .wp-block-button__link:after {
  background: currentColor;
  mask-image: url(../images/arrow-right.svg);
  mask-repeat: no-repeat;
  content: "";
  display: block;
  flex-shrink: 0;
  height: 12px;
  position: relative;
  transition: transform 0.1s ease-in;
  width: 12px;
}
.is-style-arrow .wp-block-button__link:hover:after, .is-style-arrow .wp-block-button__link:focus:after {
  transform: translateX(4px);
  transition: transform 0.2s ease-out;
}

.is-style-arrow-down .wp-block-button__link:after,
.wp-block-file .wp-block-file__button:after {
  mask-image: url(../images/download.svg);
  mask-repeat: no-repeat;
  flex: 0 0 18px;
  height: 18px;
  width: 18px;
}

.wp-block-file a.wp-block-file__button:hover,
.wp-block-file a.wp-block-file__button:focus {
  color: #1a1a1a;
}

.has-dias-primary-background-color {
  background: var(--background, #1c536a);
  border-color: var(--background, #1c536a);
}

.has-dias-primary-color {
  color: #1c536a;
}
.has-dias-primary-color > a {
  color: currentColor;
}
.has-dias-primary-color > a:hover, .has-dias-primary-color > a:focus-visible {
  text-decoration-color: rgba(28, 83, 106, 0.6);
}
.has-dias-primary-color.wp-block-button__link, .has-dias-primary-color.wp-block-button__link:hover, .has-dias-primary-color.wp-block-button__link:visited:not(:hover) {
  color: #1c536a;
}

.has-dias-secondary-background-color {
  background: var(--background, #a8d3c9);
  border-color: var(--background, #a8d3c9);
}

.has-dias-secondary-color {
  color: #a8d3c9;
}
.has-dias-secondary-color > a {
  color: currentColor;
}
.has-dias-secondary-color > a:hover, .has-dias-secondary-color > a:focus-visible {
  text-decoration-color: rgba(168, 211, 201, 0.6);
}
.has-dias-secondary-color.wp-block-button__link, .has-dias-secondary-color.wp-block-button__link:hover, .has-dias-secondary-color.wp-block-button__link:visited:not(:hover) {
  color: #a8d3c9;
}

.has-dias-tertiary-background-color {
  background: var(--background, #79144e);
  border-color: var(--background, #79144e);
}

.has-dias-tertiary-color {
  color: #79144e;
}
.has-dias-tertiary-color > a {
  color: currentColor;
}
.has-dias-tertiary-color > a:hover, .has-dias-tertiary-color > a:focus-visible {
  text-decoration-color: rgba(121, 20, 78, 0.6);
}
.has-dias-tertiary-color.wp-block-button__link, .has-dias-tertiary-color.wp-block-button__link:hover, .has-dias-tertiary-color.wp-block-button__link:visited:not(:hover) {
  color: #79144e;
}

.has-dias-white-background-color {
  background: var(--background, #fff);
  border-color: var(--background, #fff);
}

.has-dias-white-color {
  color: #fff;
}
.has-dias-white-color > a {
  color: currentColor;
}
.has-dias-white-color > a:hover, .has-dias-white-color > a:focus-visible {
  text-decoration-color: rgba(255, 255, 255, 0.6);
}
.has-dias-white-color.wp-block-button__link, .has-dias-white-color.wp-block-button__link:hover, .has-dias-white-color.wp-block-button__link:visited:not(:hover) {
  color: #fff;
}

.has-dias-black-background-color {
  background: var(--background, #000);
  border-color: var(--background, #000);
}

.has-dias-black-color {
  color: #000;
}
.has-dias-black-color > a {
  color: currentColor;
}
.has-dias-black-color > a:hover, .has-dias-black-color > a:focus-visible {
  text-decoration-color: rgba(0, 0, 0, 0.6);
}
.has-dias-black-color.wp-block-button__link, .has-dias-black-color.wp-block-button__link:hover, .has-dias-black-color.wp-block-button__link:visited:not(:hover) {
  color: #000;
}

.has-dias-grey-light-background-color {
  background: var(--background, #d7d7d7);
  border-color: var(--background, #d7d7d7);
}

.has-dias-grey-light-color {
  color: #d7d7d7;
}
.has-dias-grey-light-color > a {
  color: currentColor;
}
.has-dias-grey-light-color > a:hover, .has-dias-grey-light-color > a:focus-visible {
  text-decoration-color: rgba(215, 215, 215, 0.6);
}
.has-dias-grey-light-color.wp-block-button__link, .has-dias-grey-light-color.wp-block-button__link:hover, .has-dias-grey-light-color.wp-block-button__link:visited:not(:hover) {
  color: #d7d7d7;
}

.has-dias-grey-dark-background-color {
  background: var(--background, #193b39);
  border-color: var(--background, #193b39);
}

.has-dias-grey-dark-color {
  color: #193b39;
}
.has-dias-grey-dark-color > a {
  color: currentColor;
}
.has-dias-grey-dark-color > a:hover, .has-dias-grey-dark-color > a:focus-visible {
  text-decoration-color: rgba(25, 59, 57, 0.6);
}
.has-dias-grey-dark-color.wp-block-button__link, .has-dias-grey-dark-color.wp-block-button__link:hover, .has-dias-grey-dark-color.wp-block-button__link:visited:not(:hover) {
  color: #193b39;
}

.has-dias-base-background-color {
  background: var(--background, #1a1a1a);
  border-color: var(--background, #1a1a1a);
}

.has-dias-base-color {
  color: #1a1a1a;
}
.has-dias-base-color > a {
  color: currentColor;
}
.has-dias-base-color > a:hover, .has-dias-base-color > a:focus-visible {
  text-decoration-color: rgba(26, 26, 26, 0.6);
}
.has-dias-base-color.wp-block-button__link, .has-dias-base-color.wp-block-button__link:hover, .has-dias-base-color.wp-block-button__link:visited:not(:hover) {
  color: #1a1a1a;
}

.has-dias-highlight-background-color {
  background: var(--background, #f6be46);
  border-color: var(--background, #f6be46);
}

.has-dias-highlight-color {
  color: #f6be46;
}
.has-dias-highlight-color > a {
  color: currentColor;
}
.has-dias-highlight-color > a:hover, .has-dias-highlight-color > a:focus-visible {
  text-decoration-color: rgba(246, 190, 70, 0.6);
}
.has-dias-highlight-color.wp-block-button__link, .has-dias-highlight-color.wp-block-button__link:hover, .has-dias-highlight-color.wp-block-button__link:visited:not(:hover) {
  color: #f6be46;
}

.has-dias-transparent-background-color {
  background: var(--background, rgba(238, 238, 238, 0));
  border-color: var(--background, rgba(238, 238, 238, 0));
}

.has-dias-transparent-color {
  color: rgba(238, 238, 238, 0);
}
.has-dias-transparent-color > a {
  color: currentColor;
}
.has-dias-transparent-color > a:hover, .has-dias-transparent-color > a:focus-visible {
  text-decoration-color: rgba(238, 238, 238, 0.6);
}
.has-dias-transparent-color.wp-block-button__link, .has-dias-transparent-color.wp-block-button__link:hover, .has-dias-transparent-color.wp-block-button__link:visited:not(:hover) {
  color: rgba(238, 238, 238, 0);
}

:where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .wp-block-cover__inner-container,
:where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .components-drop-zone + .wp-block-cover__inner-container,
.wp-block-media-text:is(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color) > .wp-block-media-text__content {
  color: #fff;
}
:where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .wp-block-cover__inner-container a:not(.wp-block-button__link),
:where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .components-drop-zone + .wp-block-cover__inner-container a:not(.wp-block-button__link),
.wp-block-media-text:is(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color) > .wp-block-media-text__content a:not(.wp-block-button__link) {
  color: #fff;
}
:where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .wp-block-cover__inner-container .c-accordion__item.is-style-details .c-accordion__title,
:where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .components-drop-zone + .wp-block-cover__inner-container .c-accordion__item.is-style-details .c-accordion__title,
.wp-block-media-text:is(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color) > .wp-block-media-text__content .c-accordion__item.is-style-details .c-accordion__title {
  --color-accordion-link: #fff;
}
:where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .wp-block-cover__inner-container .c-accordion__item.is-style-details .c-accordion__content,
:where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .components-drop-zone + .wp-block-cover__inner-container .c-accordion__item.is-style-details .c-accordion__content,
.wp-block-media-text:is(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color) > .wp-block-media-text__content .c-accordion__item.is-style-details .c-accordion__content {
  --color-accordion-text: #fff;
  --color-accordion-background: none;
}

.wp-block-button__link:is(.has-dias-primary-background-color):not(.has-link-color) {
  --background: rgb(73.4, 117.4, 135.8);
  --background-hover: #1c536a;
}

.wp-block-button__link:is(.has-dias-white-background-color):not(.has-link-color) {
  --background: rgb(246.3, 250.6, 249.6);
  --background-hover: #fff;
}

.wp-block-button__link:is(.has-dias-black-background-color):not(.has-link-color) {
  --background: #000;
  --background-hover: #000;
}

.wp-block-button__link:is(.has-dias-secondary-background-color, .has-dias-white-background-color, .has-dias-grey-light-background-color):not(.has-link-color) {
  color: #1c536a;
}
.wp-block-button__link:is(.has-dias-secondary-background-color, .has-dias-white-background-color, .has-dias-grey-light-background-color):not(.has-link-color):is(:hover, :hover:visited, :focus) {
  color: #1c536a;
}

.wp-block-button__link.has-dias-transparent-background-color {
  background: transparent;
}

.is-style-outline .wp-block-button__link.has-dias-transparent-background-color {
  position: relative;
}
.is-style-outline .wp-block-button__link.has-dias-transparent-background-color:after {
  border: solid 1px white;
  content: "";
  opacity: 0;
  position: absolute;
  inset: 0;
  transition: opacity 0.2s ease-in-out;
}
.is-style-outline .wp-block-button__link.has-dias-transparent-background-color:hover, .is-style-outline .wp-block-button__link.has-dias-transparent-background-color:focus {
  border: solid 1px currentColor;
  color: currentColor;
  text-decoration: none;
}
.is-style-outline .wp-block-button__link.has-dias-transparent-background-color:hover:after, .is-style-outline .wp-block-button__link.has-dias-transparent-background-color:focus:after {
  opacity: 1;
}

.is-style-outline .wp-block-button__link {
  border: solid 1px currentColor;
  transition: all 0.2s ease-in-out;
}

.has-black-border {
  border: solid 1px #1a1a1a;
  padding-bottom: 0.5rem;
  padding-top: 0.5rem;
}

blockquote {
  background: rgb(209.6, 220.6, 225.2);
  border-left: 4px solid #1c536a;
  padding-left: 1.125rem;
  padding: 0.75rem;
}

:is(.wp-block-quote, .wp-block-pullquote) {
  font-style: normal;
  font-weight: 300;
  font-size: 28px;
  line-height: 36px;
  padding: 1.5rem;
  background: rgb(209.6, 220.6, 225.2);
  border-left: 4px solid #1c536a;
  padding: 1.5rem;
}
:is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-primary-background-color {
  background: rgb(209.6, 220.6, 225.2) !important;
  border-left-color: #1c536a;
}
:is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-secondary-background-color {
  background: rgb(237.6, 246.2, 244.2) !important;
  border-left-color: #a8d3c9;
}
:is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-tertiary-background-color {
  background: rgb(228.2, 208, 219.6) !important;
  border-left-color: #79144e;
}
:is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-white-background-color {
  background: white !important;
  border-left-color: #fff;
}
:is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-black-background-color {
  background: #cccccc !important;
  border-left-color: #000;
}
:is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-grey-light-background-color {
  background: #f7f7f7 !important;
  border-left-color: #d7d7d7;
}
:is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-grey-dark-background-color {
  background: rgb(209, 215.8, 215.4) !important;
  border-left-color: #193b39;
}
:is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-base-background-color {
  background: rgb(209.2, 209.2, 209.2) !important;
  border-left-color: #1a1a1a;
}
:is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-highlight-background-color {
  background: rgb(253.2, 242, 218) !important;
  border-left-color: #f6be46;
}
:is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-transparent-background-color {
  background: rgba(255, 255, 255, 0.8) !important;
  border-left-color: rgba(238, 238, 238, 0);
}
:is(.wp-block-quote, .wp-block-pullquote) blockquote {
  border: 0;
  padding: 0;
}
:is(.wp-block-quote, .wp-block-pullquote) blockquote p:first-child {
  margin-top: 0;
}

.line-clamp-1,
.line-clamp-2,
.line-clamp-3,
.line-clamp-4,
.line-clamp-5 {
  --lines: 1;
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: var(--lines);
  -webkit-box-orient: vertical;
}

.line-clamp-2 {
  --lines: 2;
}

.line-clamp-3 {
  --lines: 3;
}

.line-clamp-4 {
  --lines: 4;
}

.line-clamp-5 {
  --lines: 5;
}

.c-accordion__item {
  background: rgb(232.3, 237.8, 240.1);
  border: 0;
  border-left: 4px solid #1c536a;
  box-shadow: 0px 4px 12px 0px rgba(21, 55, 53, 0.15);
  color: #1a1a1a;
  margin-bottom: 0.5rem;
  margin-top: 1.5rem;
  padding: 0;
  position: relative;
  transition: all 0.2s ease-in-out;
}
.c-accordion__item:hover {
  background: rgb(209.6, 220.6, 225.2);
  box-shadow: 0px 4px 16px 0px rgba(21, 55, 53, 0.4);
}
.c-accordion__item.is-open {
  border-bottom-width: 1px;
}
.c-accordion__item + .c-accordion__item {
  margin-top: 2.5rem;
}

.c-accordion__title {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 24px;
  letter-spacing: 0;
  line-height: 28px;
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin: 0 1.5rem;
  padding: 1.5rem 0;
  position: static;
  outline: 0;
  text-decoration: none;
}
.c-accordion__title:hover {
  text-decoration: underline;
}
.c-accordion__title:focus-visible {
  background: #f6be46;
  box-shadow: 0 -2px #f6be46, 0 4px #1a1a1a;
  color: #1a1a1a !important;
  outline: 4px solid rgba(0, 0, 0, 0);
  text-decoration: none !important;
}
.c-accordion__title:after {
  background: #000;
  mask-image: url(../images/accordion.svg);
  mask-repeat: no-repeat;
  content: "";
  display: inline-block;
  height: 24px;
  margin-right: 0.5rem;
  position: relative;
  top: auto;
  transform: none;
  vertical-align: middle;
  width: 24px;
}
.is-open > .c-accordion__title:after {
  mask-image: url(../images/accordion-expanded.svg);
}

.c-accordion__content {
  margin: -1.5rem 0 0;
  padding: 1.5rem;
  position: relative;
  z-index: 2;
}
.c-accordion__content > p:first-child {
  margin-top: 0;
}

.wp-block-details {
  box-shadow: inset 4px 0 #d8dde0;
  margin-top: 8px;
  padding: 0 16px 0 20px;
}
.wp-block-details:open {
  padding: 0 16px 16px 20px;
}
.wp-block-details summary {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
  background: #fff;
  color: #1c536a;
  display: inline-block;
  margin-left: -20px;
  margin-top: -16px;
  padding: 0 0 0 24px;
  position: relative;
}
.wp-block-details summary + * {
  margin-top: 12px;
}
.wp-block-details summary:focus-visible {
  background-color: #ffeb3b;
  box-shadow: 0 -2px #ffeb3b, 0 4px #212b32;
  color: #212b32;
  outline: 4px solid rgba(0, 0, 0, 0);
  text-decoration: none;
}
.wp-block-details summary:before {
  background: none;
  border-style: solid;
  border-width: 7px 0 7px 12px;
  border-color: transparent transparent transparent currentColor;
  display: block;
  height: 0;
  left: 0;
  top: calc(50% - 10px);
  width: 0;
}

.c-accordion__item.is-style-details {
  --color-accordion-link: #1c536a;
  --color-accordion-text: #1a1a1a;
  --color-accordion-background: none;
  background: none;
  border: 0;
  margin-top: 3rem;
  padding-bottom: 0;
  padding-top: 0;
}
.c-accordion__item.is-style-details .c-accordion__title {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
  color: var(--color-accordion-link);
  display: inline-block;
  margin: 0;
}
.c-accordion__item.is-style-details .c-accordion__title:focus-visible {
  background-color: #ffeb3b;
  box-shadow: 0 -2px #ffeb3b, 0 4px #212b32;
  color: #212b32;
  outline: 4px solid rgba(0, 0, 0, 0);
  text-decoration: none;
}
.c-accordion__item.is-style-details .c-accordion__title:after {
  display: none;
}
.c-accordion__item.is-style-details .c-accordion__title:before {
  background: none;
  mask-image: none;
  border-style: solid;
  border-width: 7px 0 7px 12px;
  border-color: transparent transparent transparent currentColor;
  display: inline-block;
  height: 0;
  margin-right: 0.75rem;
  margin-top: -8px;
  vertical-align: middle;
  width: 0;
}
.c-accordion__item.is-style-details .c-accordion__content {
  background: var(--color-accordion-background);
  border-left: 4px solid #d8dde0;
  color: var(--color-accordion-text);
  margin-top: -1.5rem;
  padding: 1.5rem;
  padding-left: 20px;
}
.c-accordion__item.is-style-details.is-open .c-accordion__title:before {
  transform: rotate(90deg);
}

.wp-block-social-links:not(.is-style-logos-only) .wp-social-link {
  background: #1a1a1a;
  color: #fff;
  height: 40px;
  transition: all 0.2s ease-in-out;
  width: 40px;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link:hover, .wp-block-social-links:not(.is-style-logos-only) .wp-social-link:focus {
  background: #1a1a1a;
  transform: none;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link a {
  background: none;
  border-radius: 50%;
  height: 40px;
  position: relative;
  width: 40px;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link a:hover, .wp-block-social-links:not(.is-style-logos-only) .wp-social-link a:focus {
  background-size: 0;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link a:focus-visible {
  background: #1a1a1a;
  color: #fff !important;
  outline: #000 auto 5px;
  border: solid 4px #f6be46;
}
.wp-block-social-links:not(.is-style-logos-only) .wp-social-link a svg {
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
}

ul,
ol {
  list-style-position: outside;
  list-style-type: disc;
}
ul li,
ol li {
  margin-left: 1.5rem;
}
ul li:where(:not(:last-child)),
ol li:where(:not(:last-child)) {
  margin-bottom: 0.75rem;
}

ol {
  list-style: decimal;
}

.wp-block-separator:not(.is-style-dots) {
  border-bottom: 1px solid rgb(94.7, 94.7, 94.7);
  margin-bottom: 2.5rem;
  margin-top: 2.5rem;
  opacity: 0.5;
}
.wp-block-separator:not(.is-style-dots).is-style-wide {
  margin-top: 72px;
}
.wp-block-separator:not(.is-style-dots):first-child {
  margin-top: 0;
}
.wp-block-separator:not(.is-style-dots).has-background:not(.is-style-dots) {
  border-top-style: solid;
  border-top-width: 1px;
  border-color: currentColor;
  height: 0;
}

.wp-block-social-link a {
  background: none;
}

.has-background :where(a) {
  color: currentColor;
}

.wp-block-footnotes {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
}

sup.fn a {
  padding: 0 3px;
}
sup.fn::before {
  content: "(";
}
sup.fn::after {
  content: ")";
}

:where(.entry-content, .site-main) {
  position: relative;
}
:where(.entry-content, .site-main) > *, :where(.entry-content, .site-main) > .gform_legacy_markup_wrapper, :where(.entry-content, .site-main) > .wp-block, :where(.entry-content, .site-main) > .wp-block-image, :where(.entry-content, .site-main) > .wp-block-table, :where(.entry-content, .site-main) > .wp-block-group, :where(.entry-content, .site-main) > .wp-block-cover.aligncenter,
:where(.entry-content, .site-main) .wp-block-group.alignfull > *, :where(.entry-content, .site-main) > .acf-block-body > *, :where(.entry-content, .site-main) > .wpdt-c.wpDataTableContainerSimpleTable.wdtscroll.wdtscroll, :where(.entry-content, .site-main) > .wp-block-separator:not(.is-style-wide):not(.is-style-dots),
:where(.entry-content, .site-main) .wp-block-cover__inner-container > * {
  --wp--style--global--content-size: 51.25rem;
  margin-left: auto;
  margin-right: auto;
  max-width: 51.25rem;
  width: calc(100% - var(--side-gutter) * 2);
}
:where(body.single-snapshot) :where(.entry-content, .site-main) > *, :where(body.single-snapshot) :where(.entry-content, .site-main) > .gform_legacy_markup_wrapper, :where(body.single-snapshot) :where(.entry-content, .site-main) > .wp-block, :where(body.single-snapshot) :where(.entry-content, .site-main) > .wp-block-image, :where(body.single-snapshot) :where(.entry-content, .site-main) > .wp-block-table, :where(body.single-snapshot) :where(.entry-content, .site-main) > .wp-block-group, :where(body.single-snapshot) :where(.entry-content, .site-main) > .wp-block-cover.aligncenter,
:where(body.single-snapshot) :where(.entry-content, .site-main) .wp-block-group.alignfull > *, :where(body.single-snapshot) :where(.entry-content, .site-main) > .acf-block-body > *, :where(body.single-snapshot) :where(.entry-content, .site-main) > .wpdt-c.wpDataTableContainerSimpleTable.wdtscroll.wdtscroll, :where(body.single-snapshot) :where(.entry-content, .site-main) > .wp-block-separator:not(.is-style-wide):not(.is-style-dots),
:where(body.single-snapshot) :where(.entry-content, .site-main) .wp-block-cover__inner-container > * {
  max-width: 960px;
}
:where(.entry-content, .site-main).entry-content--wide {
  container-type: inline-size;
  container-name: content-size;
  margin: 0;
}
:where(.entry-content, .site-main).entry-content--wide > *:not(.alignfull, .wp-block-cover), :where(.entry-content, .site-main).entry-content--wide > .gform_legacy_markup_wrapper:not(.alignfull, .wp-block-cover), :where(.entry-content, .site-main).entry-content--wide > .wp-block:not(.alignfull, .wp-block-cover), :where(.entry-content, .site-main).entry-content--wide > .wp-block-image:not(.alignfull, .wp-block-cover), :where(.entry-content, .site-main).entry-content--wide > .wp-block-table:not(.alignfull, .wp-block-cover), :where(.entry-content, .site-main).entry-content--wide > .wp-block-group:not(.alignfull, .wp-block-cover),
:where(.entry-content, .site-main).entry-content--wide .wp-block-group.alignfull > *:not(.alignfull, .wp-block-cover), :where(.entry-content, .site-main).entry-content--wide > .acf-block-body > *:not(.alignfull, .wp-block-cover), :where(.entry-content, .site-main).entry-content--wide > .wp-block-separator:not(.is-style-wide):not(.is-style-dots):not(.alignfull, .wp-block-cover),
:where(.entry-content, .site-main).entry-content--wide .wp-block-cover__inner-container > *:not(.alignfull, .wp-block-cover) {
  margin-left: var(--side-gutter);
}
@container content-size (width > 1224px) {
  :where(.entry-content, .site-main).entry-content--wide > *:not(.alignfull, .wp-block-cover), :where(.entry-content, .site-main).entry-content--wide > .gform_legacy_markup_wrapper:not(.alignfull, .wp-block-cover), :where(.entry-content, .site-main).entry-content--wide > .wp-block:not(.alignfull, .wp-block-cover), :where(.entry-content, .site-main).entry-content--wide > .wp-block-image:not(.alignfull, .wp-block-cover), :where(.entry-content, .site-main).entry-content--wide > .wp-block-table:not(.alignfull, .wp-block-cover), :where(.entry-content, .site-main).entry-content--wide > .wp-block-group:not(.alignfull, .wp-block-cover),
  :where(.entry-content, .site-main).entry-content--wide .wp-block-group.alignfull > *:not(.alignfull, .wp-block-cover), :where(.entry-content, .site-main).entry-content--wide > .acf-block-body > *:not(.alignfull, .wp-block-cover), :where(.entry-content, .site-main).entry-content--wide > .wp-block-separator:not(.is-style-wide):not(.is-style-dots):not(.alignfull, .wp-block-cover),
  :where(.entry-content, .site-main).entry-content--wide .wp-block-cover__inner-container > *:not(.alignfull, .wp-block-cover) {
    margin-left: calc((100% - 75rem) / 2);
  }
}
:where(.entry-content, .site-main) :is(.wp-block-column) .wp-block-cover {
  padding: 0;
}
:where(.entry-content, .site-main) :is(.wp-block-column) .wp-block-cover__inner-container {
  padding: 1.5rem;
}
:where(.entry-content, .site-main) :is(.wp-block-column) .wp-block-cover__inner-container > * {
  margin-left: auto !important;
  width: auto;
}
:where(.entry-content, .site-main) > iframe {
  display: block;
}
:where(.entry-content, .site-main) .wp-block-cover {
  padding-right: 0;
  padding-left: 0;
}
:where(.entry-content, .site-main) [data-align=full],
:where(.entry-content, .site-main) .wp-block-cover,
:where(.entry-content, .site-main) .alignfull,
:where(.entry-content, .site-main) .is-style-full {
  --wp--style--global--content-size: none;
  max-width: none;
  width: 100%;
}
:where(.entry-content, .site-main) [data-align=wide],
:where(.entry-content, .site-main) .alignwide,
:where(.entry-content, .site-main) .is-style-wide,
:where(.entry-content, .site-main) .wp-block-cover__inner-container .alignwide,
:where(.entry-content, .site-main) .wp-block-cover.is-style-floating-image {
  --wp--style--global--content-size: 75rem;
  max-width: 75rem;
}
:where(.entry-content, .site-main) .wp-block-cover,
:where(.entry-content, .site-main) .wp-block-cover-image {
  min-height: 300px;
}
:where(.entry-content, .site-main) .wp-block-cover.wp-block-cover {
  border-radius: 4px;
}
:where(.entry-content, .site-main) > .wp-block-cover.wp-block-cover {
  padding-bottom: 72px;
  padding-top: 72px;
}
:where(.entry-content, .site-main) > .wp-block-cover.wp-block-cover:not(.aligncenter, .alignwide) {
  border-radius: 0;
}
@media (max-width: 678px) {
  :where(.entry-content, .site-main) :where(.wp-block-group.has-background) {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
:where(.entry-content, .site-main) > .wp-block[data-align=center] .wp-block-cover,
:where(.entry-content, .site-main) > .wp-block-cover.aligncenter {
  padding: 1.5rem;
}
:where(.entry-content, .site-main) > .wp-block[data-align=center] .wp-block-cover > .wp-block-cover__inner-container > *,
:where(.entry-content, .site-main) > .wp-block-cover.aligncenter > .wp-block-cover__inner-container > * {
  width: auto;
}
:where(.entry-content, .site-main) .wp-block-preformatted {
  background-color: #153634;
  border-left: 11px solid;
  border-radius: 8px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  color: #ffffff;
  font-size: 14px;
  padding: 0.75rem 1.125rem;
  line-height: 1.3;
  white-space: pre-wrap;
}
:where(.entry-content, .site-main) mark.has-inline-color {
  transition: color 0.2s ease-out 0.5s;
}
@media (min-width: 1351px) {
  :where(.entry-content, .site-main) .wp-block-group:has(> .wp-block-cover.is-style-card-link) {
    gap: 72px;
  }
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link {
  flex: 0 1 50%;
  max-width: 352px;
  min-width: 220px;
  min-height: 220px;
  padding: 3rem 1.5rem;
  position: relative;
  overflow: visible;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link a:before {
  content: "";
  inset: 0;
  position: absolute;
  z-index: 2;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link > .wp-block-cover__background {
  border: solid 1px var(--border-color, transparent);
  opacity: 1;
  transition: background 0.2s ease-out;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__inner-container {
  text-align: center;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__inner-container > * {
  --side-gutter: 0;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__inner-container .wp-block-heading {
  margin-top: 0.75rem;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__inner-container ul.is-style-list-style-none,
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__inner-container ol.is-style-list-style-none {
  margin-top: 0;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__inner-container :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  transform: scale(var(--scale, 1));
  transition: transform 0.2s ease-out;
  transform-origin: center bottom;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-primary-background-color {
  --border-color: rgb(141.5, 169, 180.5);
  --background: rgb(198.25, 212, 217.75);
  opacity: 1;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-secondary-background-color {
  --border-color: rgb(211.5, 233, 228);
  --background: rgb(233.25, 244, 241.5);
  opacity: 1;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-tertiary-background-color {
  --border-color: rgb(188, 137.5, 166.5);
  --background: rgb(221.5, 196.25, 210.75);
  opacity: 1;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-white-background-color {
  --border-color: white;
  --background: white;
  opacity: 1;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-black-background-color {
  --border-color: rgb(127.5, 127.5, 127.5);
  --background: rgb(191.25, 191.25, 191.25);
  opacity: 1;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-grey-light-background-color {
  --border-color: #ebebeb;
  --background: whitesmoke;
  opacity: 1;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-grey-dark-background-color {
  --border-color: #8c9d9c;
  --background: rgb(197.5, 206, 205.5);
  opacity: 1;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-base-background-color {
  --border-color: rgb(140.5, 140.5, 140.5);
  --background: rgb(197.75, 197.75, 197.75);
  opacity: 1;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-highlight-background-color {
  --border-color: rgb(250.5, 222.5, 162.5);
  --background: rgb(252.75, 238.75, 208.75);
  opacity: 1;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-transparent-background-color {
  --border-color: rgba(255, 255, 255, 0.5);
  --background: rgba(255, 255, 255, 0.75);
  opacity: 1;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-primary-background-color {
  --border-color: rgb(84.75, 126, 143.25);
  --background: rgb(141.5, 169, 180.5);
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-secondary-background-color {
  --border-color: rgb(189.75, 222, 214.5);
  --background: rgb(211.5, 233, 228);
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-tertiary-background-color {
  --border-color: rgb(154.5, 78.75, 122.25);
  --background: rgb(188, 137.5, 166.5);
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-white-background-color {
  --border-color: white;
  --background: white;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-black-background-color {
  --border-color: rgb(63.75, 63.75, 63.75);
  --background: rgb(127.5, 127.5, 127.5);
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-grey-light-background-color {
  --border-color: #e1e1e1;
  --background: #ebebeb;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-grey-dark-background-color {
  --border-color: rgb(82.5, 108, 106.5);
  --background: #8c9d9c;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-base-background-color {
  --border-color: rgb(83.25, 83.25, 83.25);
  --background: rgb(140.5, 140.5, 140.5);
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-highlight-background-color {
  --border-color: rgb(248.25, 206.25, 116.25);
  --background: rgb(250.5, 222.5, 162.5);
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-transparent-background-color {
  --border-color: rgba(255, 255, 255, 0.25);
  --background: rgba(255, 255, 255, 0.5);
}
:where(.entry-content, .site-main) .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
:where(.entry-content, .site-main) .is-style-bordered {
  border-left: 4px solid #1c536a;
  padding-left: 1.5rem;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered {
  border-left: 4px solid #1c536a;
  background: rgb(232.3, 237.8, 240.1);
  padding: 1.5rem;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered .wp-block-heading:not(.has-text-color) {
  color: rgb(22.4, 66.4, 84.8);
}
:where(.entry-content, .site-main) ul.is-style-bordered {
  padding: 0 0 0 1.5rem;
}
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-primary-background-color + .wp-block-cover__inner-container) .is-style-bordered,
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-primary-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #1c536a;
}
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-secondary-background-color + .wp-block-cover__inner-container) .is-style-bordered,
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-secondary-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #a8d3c9;
}
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-tertiary-background-color + .wp-block-cover__inner-container) .is-style-bordered,
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-tertiary-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #79144e;
}
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-white-background-color + .wp-block-cover__inner-container) .is-style-bordered,
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-white-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #fff;
}
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-black-background-color + .wp-block-cover__inner-container) .is-style-bordered,
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-black-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #000;
}
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-grey-light-background-color + .wp-block-cover__inner-container) .is-style-bordered,
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-grey-light-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #d7d7d7;
}
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-grey-dark-background-color + .wp-block-cover__inner-container) .is-style-bordered,
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-grey-dark-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #193b39;
}
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-base-background-color + .wp-block-cover__inner-container) .is-style-bordered,
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-base-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #1a1a1a;
}
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-highlight-background-color + .wp-block-cover__inner-container) .is-style-bordered,
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-highlight-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #f6be46;
}
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-transparent-background-color + .wp-block-cover__inner-container) .is-style-bordered,
:where(.entry-content, .site-main) :where(.wp-block-cover__background.has-dias-transparent-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: rgba(238, 238, 238, 0);
}
:where(.entry-content, .site-main) .is-style-bordered.has-dias-primary-background-color {
  border-left-color: #1c536a;
  background: transparent !important;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-primary-background-color {
  background: rgb(232.3, 237.8, 240.1) !important;
  border-left-color: #1c536a;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-primary-background-color .wp-block-heading:not(.has-text-color) {
  color: rgb(22.4, 66.4, 84.8);
}
:where(.entry-content, .site-main) .is-style-bordered.has-dias-secondary-background-color {
  border-left-color: #a8d3c9;
  background: transparent !important;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-secondary-background-color {
  background: rgb(246.3, 250.6, 249.6) !important;
  border-left-color: #a8d3c9;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-secondary-background-color .wp-block-heading:not(.has-text-color) {
  color: rgb(134.4, 168.8, 160.8);
}
:where(.entry-content, .site-main) .is-style-bordered.has-dias-tertiary-background-color {
  border-left-color: #79144e;
  background: transparent !important;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-tertiary-background-color {
  background: rgb(241.6, 231.5, 237.3) !important;
  border-left-color: #79144e;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-tertiary-background-color .wp-block-heading:not(.has-text-color) {
  color: rgb(96.8, 16, 62.4);
}
:where(.entry-content, .site-main) .is-style-bordered.has-dias-white-background-color {
  border-left-color: #fff;
  background: transparent !important;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-white-background-color {
  background: white !important;
  border-left-color: #fff;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-white-background-color .wp-block-heading:not(.has-text-color) {
  color: #cccccc;
}
:where(.entry-content, .site-main) .is-style-bordered.has-dias-black-background-color {
  border-left-color: #000;
  background: transparent !important;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-black-background-color {
  background: rgb(229.5, 229.5, 229.5) !important;
  border-left-color: #000;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-black-background-color .wp-block-heading:not(.has-text-color) {
  color: black;
}
:where(.entry-content, .site-main) .is-style-bordered.has-dias-grey-light-background-color {
  border-left-color: #d7d7d7;
  background: transparent !important;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-grey-light-background-color {
  background: #fbfbfb !important;
  border-left-color: #d7d7d7;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-grey-light-background-color .wp-block-heading:not(.has-text-color) {
  color: #acacac;
}
:where(.entry-content, .site-main) .is-style-bordered.has-dias-grey-dark-background-color {
  border-left-color: #193b39;
  background: transparent !important;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-grey-dark-background-color {
  background: rgb(232, 235.4, 235.2) !important;
  border-left-color: #193b39;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-grey-dark-background-color .wp-block-heading:not(.has-text-color) {
  color: rgb(20, 47.2, 45.6);
}
:where(.entry-content, .site-main) .is-style-bordered.has-dias-base-background-color {
  border-left-color: #1a1a1a;
  background: transparent !important;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-base-background-color {
  background: rgb(232.1, 232.1, 232.1) !important;
  border-left-color: #1a1a1a;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-base-background-color .wp-block-heading:not(.has-text-color) {
  color: rgb(20.8, 20.8, 20.8);
}
:where(.entry-content, .site-main) .is-style-bordered.has-dias-highlight-background-color {
  border-left-color: #f6be46;
  background: transparent !important;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-highlight-background-color {
  background: rgb(254.1, 248.5, 236.5) !important;
  border-left-color: #f6be46;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-highlight-background-color .wp-block-heading:not(.has-text-color) {
  color: rgb(196.8, 152, 56);
}
:where(.entry-content, .site-main) .is-style-bordered.has-dias-transparent-background-color {
  border-left-color: rgba(238, 238, 238, 0);
  background: transparent !important;
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-transparent-background-color {
  background: rgba(255, 255, 255, 0.9) !important;
  border-left-color: rgba(238, 238, 238, 0);
}
:where(.entry-content, .site-main) .wp-block-group.is-style-bordered.has-dias-transparent-background-color .wp-block-heading:not(.has-text-color) {
  color: rgba(0, 0, 0, 0.2);
}
:where(.entry-content, .site-main) .is-style-boxed {
  background: rgb(232.3, 237.8, 240.1);
  border: 1px solid rgb(141.5, 169, 180.5);
  min-height: 260px;
  padding: 1.5rem;
}
:where(.entry-content, .site-main) .is-style-boxed.has-dias-primary-background-color {
  background-color: rgb(232.3, 237.8, 240.1) !important;
  border-color: rgb(141.5, 169, 180.5);
}
:where(.entry-content, .site-main) .is-style-boxed.has-dias-secondary-background-color {
  background-color: rgb(246.3, 250.6, 249.6) !important;
  border-color: rgb(211.5, 233, 228);
}
:where(.entry-content, .site-main) .is-style-boxed.has-dias-tertiary-background-color {
  background-color: rgb(241.6, 231.5, 237.3) !important;
  border-color: rgb(188, 137.5, 166.5);
}
:where(.entry-content, .site-main) .is-style-boxed.has-dias-white-background-color {
  background-color: white !important;
  border-color: white;
}
:where(.entry-content, .site-main) .is-style-boxed.has-dias-black-background-color {
  background-color: rgb(229.5, 229.5, 229.5) !important;
  border-color: rgb(127.5, 127.5, 127.5);
}
:where(.entry-content, .site-main) .is-style-boxed.has-dias-grey-light-background-color {
  background-color: #fbfbfb !important;
  border-color: #ebebeb;
}
:where(.entry-content, .site-main) .is-style-boxed.has-dias-grey-dark-background-color {
  background-color: rgb(232, 235.4, 235.2) !important;
  border-color: #8c9d9c;
}
:where(.entry-content, .site-main) .is-style-boxed.has-dias-base-background-color {
  background-color: rgb(232.1, 232.1, 232.1) !important;
  border-color: rgb(140.5, 140.5, 140.5);
}
:where(.entry-content, .site-main) .is-style-boxed.has-dias-highlight-background-color {
  background-color: rgb(254.1, 248.5, 236.5) !important;
  border-color: rgb(250.5, 222.5, 162.5);
}
:where(.entry-content, .site-main) .is-style-boxed.has-dias-transparent-background-color {
  background-color: rgba(255, 255, 255, 0.9) !important;
  border-color: rgba(255, 255, 255, 0.5);
}
:where(.entry-content, .site-main) .wp-block-media-text.is-image-fill-element .wp-block-media-text__media {
  min-height: 100%;
  position: relative;
}
@media (max-width: 600px) {
  :where(.entry-content, .site-main) .wp-block-media-text.is-image-fill-element .wp-block-media-text__media {
    min-height: 220px;
  }
}
:where(.entry-content, .site-main) .wp-block-media-text.is-image-fill-element .wp-block-media-text__media img {
  display: block;
  height: 100%;
  margin: 0;
  position: absolute;
  object-fit: cover;
  transform: scale(1);
  transition: all 0.2s ease;
  width: 100%;
}
:where(.entry-content, .site-main) .button {
  align-items: flex-start;
  background: var(--background, rgb(73.4, 117.4, 135.8));
  border-radius: 0;
  border: 0;
  color: #fff;
  cursor: pointer;
  display: inline-flex;
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 18px;
  padding: 0.75rem 2rem;
  text-align: center;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
}
:where(.entry-content, .site-main) .button:hover, :where(.entry-content, .site-main) .button:hover:visited, :where(.entry-content, .site-main) .button:focus {
  background: var(--background-hover, #1c536a);
  outline: none;
  text-decoration: underline;
}
:where(.entry-content, .site-main) .button:focus-visible {
  box-shadow: inset 0px 0px 0 2px #000;
  outline: 4px solid #f6be46 !important;
  outline-offset: 0;
}
:where(.entry-content, .site-main) .wp-block-columns.is-style-space-between {
  justify-content: space-between;
}
:where(.entry-content, .site-main) .wp-block-columns.alignwide:has(.wp-block-column[style*="66.66%"]):has(.wp-block-column[style*="33.33%"]),
:where(.entry-content, .site-main) .wp-block[data-align=wide] > .wp-block-columns:has(> .wp-block-column[style*="66.66%"]):has(> .wp-block-column[style*="33.33%"]) {
  justify-content: space-between;
}
:where(.entry-content, .site-main) .wp-block-columns.alignwide:has(.wp-block-column[style*="66.66%"]):has(.wp-block-column[style*="33.33%"]) > .wp-block-column[style*="66.66%"],
:where(.entry-content, .site-main) .wp-block[data-align=wide] > .wp-block-columns:has(> .wp-block-column[style*="66.66%"]):has(> .wp-block-column[style*="33.33%"]) > .wp-block-column[style*="66.66%"] {
  flex-basis: 51.25rem !important;
}
:where(.entry-content, .site-main) .wp-block-columns.alignwide:has(.wp-block-column[style*="66.66%"]):has(.wp-block-column[style*="33.33%"]) > .wp-block-column[style*="33.33%"],
:where(.entry-content, .site-main) .wp-block[data-align=wide] > .wp-block-columns:has(> .wp-block-column[style*="66.66%"]):has(> .wp-block-column[style*="33.33%"]) > .wp-block-column[style*="33.33%"] {
  flex-basis: 368px !important;
}
:where(.entry-content, .site-main) img.alignleft {
  float: left;
  margin: 5px 1.5rem 1.5rem 0;
}
:where(.entry-content, .site-main) img.alignright {
  float: right;
  margin: 5px 0 1.5rem 1.5rem;
}
:where(.entry-content, .site-main) .is-style-list-style-none {
  list-style: none;
  margin-left: 0;
  padding-left: 0;
}
:where(.entry-content, .site-main) .is-style-list-style-none li {
  margin: 0 0 0;
  padding: 0 0 0;
}
:where(.entry-content, .site-main) .is-style-list-style-none li:not(:last-child) {
  margin-bottom: 0.5rem;
}
:where(.entry-content, .site-main) .mt-auto {
  margin-top: auto !important;
}
:where(.entry-content, .site-main) .h-full {
  height: 100%;
}
@media (min-width: 782px) {
  :where(.entry-content, .site-main) .wp-block-column > .wp-block-cover.is-style-sticky {
    position: sticky;
    top: 1.5rem;
  }
  :where(.entry-content, .site-main) .wp-block-column > .wp-block-cover.is-style-sticky > .wp-block-cover__inner-container:not(:has(> .in-page-sub-nav)) {
    max-height: calc(100vh - 48px);
    max-height: calc(100dvh - 48px);
    overflow: auto;
  }
  :where(.entry-content, .site-main) .wp-block-column > .wp-block-cover.is-style-sticky > .wp-block-cover__inner-container > .in-page-sub-nav > ul {
    max-height: calc(100vh - 150px);
    max-height: calc(100dvh - 150px);
    overflow: auto;
  }
}
:where(.entry-content, .site-main) .social-sharing {
  align-items: center;
  display: flex;
  gap: 1.5rem;
}
:where(.entry-content, .site-main) .social-sharing > p {
  margin: 0;
}
:where(.entry-content, .site-main) .social-sharing-list {
  align-items: center;
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  list-style: none;
  margin: 0;
  padding: 0;
}
:where(.entry-content, .site-main) .social-sharing-list > li {
  margin: 0;
  padding: 0;
}
:where(.entry-content, .site-main) .social-sharing-list svg {
  height: 28px;
  width: 28px;
}
:where(.entry-content, .site-main) .social-sharing-list svg path {
  fill: currentColor;
}
:where(.entry-content, .site-main) .social-sharing-list a {
  color: #000;
  display: block;
  position: relative;
}
:where(.entry-content, .site-main) .social-sharing-list a:hover,
:where(.entry-content, .site-main) .social-sharing-list a:focus {
  color: #1c536a;
  transform: scale(1.1);
  transition: all 0.2s ease-out;
}
body.single-snapshot :where(.entry-content, .site-main) .social-sharing-list a:hover,
body.single-snapshot :where(.entry-content, .site-main) .social-sharing-list a:focus {
  color: #79144e;
}
:where(.entry-content, .site-main) .in-page-sub-nav {
  background: var(--subnav-bg, #1c536a);
  color: #fff;
  max-width: none;
  position: sticky;
  top: var(--wp-admin--admin-bar--height, 0);
  width: 100%;
  z-index: 20;
}
@media screen and (max-width: 600px) {
  :where(.entry-content, .site-main) .in-page-sub-nav {
    top: 0;
  }
}
:where(.entry-content, .site-main) .in-page-sub-nav > ul {
  display: flex;
  justify-content: flex-start;
  list-style: none;
  margin: 0 auto;
  max-width: 51.25rem;
  overflow: auto;
  padding: 0;
  white-space: nowrap;
}
:where(body.single-snapshot) :where(.entry-content, .site-main) .in-page-sub-nav > ul {
  max-width: 960px;
}
:where(.entry-content, .site-main) .in-page-sub-nav > ul > li {
  margin: 0;
  padding: 0;
}
:where(.entry-content, .site-main) .in-page-sub-nav > ul > li > a {
  color: #fff;
  display: block;
  padding: 0.75rem 1.125rem;
  text-decoration: none;
  white-space: nowrap;
}
:where(.entry-content, .site-main) .in-page-sub-nav > ul > li > a.current, :where(.entry-content, .site-main) .in-page-sub-nav > ul > li > a:hover, :where(.entry-content, .site-main) .in-page-sub-nav > ul > li > a:focus {
  background: var(--subnav-hover, rgb(73.4, 117.4, 135.8));
  text-decoration: underline;
}
@media (max-width: 699px) {
  :where(.entry-content, .site-main) .in-page-sub-nav > ul > li a.wp-block-button__link {
    line-height: 28px;
    padding-bottom: 0.75rem;
    padding-top: 0.75rem;
  }
}
.single-snapshot :where(.entry-content, .site-main) .in-page-sub-nav {
  --subnav-bg: rgb(84.7, 14, 54.6);
  --subnav-hover: rgb(147.8, 67, 113.4);
}
@media (min-width: 700px) {
  :where(.entry-content, .site-main) .in-page-sub-nav--left {
    background: none;
    color: #000;
    margin-left: 1.5rem;
    width: 228px;
  }
  :where(.entry-content, .site-main) .in-page-sub-nav--left > ul {
    background: none;
    flex-direction: column;
    margin-top: 104px;
    position: absolute;
  }
  :where(.entry-content, .site-main) .in-page-sub-nav--left > ul::before {
    font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 32px;
    letter-spacing: 0;
    line-height: 40px;
    content: "Contents";
    margin-bottom: 1.5rem;
  }
}
@media (min-width: 700px) and (max-width: 600px) {
  :where(.entry-content, .site-main) .in-page-sub-nav--left > ul::before {
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    letter-spacing: 0;
    line-height: 30px;
  }
}
@media (min-width: 700px) {
  :where(.entry-content, .site-main) .in-page-sub-nav--left > ul > li {
    margin: 0;
  }
  :where(.entry-content, .site-main) .in-page-sub-nav--left > ul > li:first-child {
    margin: 0;
  }
  :where(.entry-content, .site-main) .in-page-sub-nav--left > ul > li > a {
    font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
    font-style: normal;
    font-weight: 300;
    font-size: 16px !important;
    line-height: 24px !important;
    background: none;
    color: #000;
    padding: 0 0.75rem 0.75rem;
    position: relative;
  }
  :where(.entry-content, .site-main) .in-page-sub-nav--left > ul > li > a:after {
    background: var(--border-colour, transparent);
    content: "";
    inset: 0 0 0 auto;
    position: absolute;
    width: 5px;
    transition: background 0.2s ease-out;
  }
  :where(.entry-content, .site-main) .in-page-sub-nav--left > ul > li > a.current, :where(.entry-content, .site-main) .in-page-sub-nav--left > ul > li > a:hover, :where(.entry-content, .site-main) .in-page-sub-nav--left > ul > li > a:focus {
    background: none;
    --border-colour: blue;
  }
  :where(.entry-content, .site-main) .in-page-sub-nav--left > ul .wp-block-buttons {
    flex-direction: column;
    margin-top: 1.5rem;
  }
  :where(.entry-content, .site-main) .in-page-sub-nav--left {
    margin-left: max((100vw - 51.25rem) / 2 - 228px, var(--side-gutter));
  }
  :where(.entry-content, .site-main) .two-column-layout {
    margin-left: max((100vw - 51.25rem) / 2 - 228px - 48px, var(--side-gutter));
  }
}
@media (max-width: 699px) {
  :where(.entry-content, .site-main) .two-column-layout > *:has(.in-page-sub-nav) {
    position: sticky;
    top: 0;
  }
  :where(.entry-content, .site-main) .two-column-layout .in-page-sub-nav {
    margin-left: calc(var(--side-gutter) * -1);
    margin-right: calc(var(--side-gutter) * -1);
    width: auto;
  }
}
@media (min-width: 700px) {
  :where(.entry-content, .site-main) .two-column-layout {
    display: grid;
    grid-template-columns: 220px 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
    margin-top: 3rem;
    max-width: calc(51.25rem + 220px);
  }
  :where(.entry-content, .site-main) .two-column-layout .in-page-sub-nav--left {
    margin-left: 0;
  }
  :where(.entry-content, .site-main) .two-column-layout .in-page-sub-nav--left > ul {
    position: relative;
  }
  :where(.entry-content, .site-main) .two-column-layout .in-page-sub-nav--left > ul {
    margin-top: 1.5rem;
  }
}
:where(.entry-content, .site-main) .two-column-issue-layout {
  gap: 1.5rem;
  justify-content: space-between;
  max-width: 75rem;
}
:where(.entry-content, .site-main) .issue-main-sidebar {
  padding-top: 1.5rem;
}
:where(.entry-content, .site-main) .subnav-issue-paper {
  max-height: 100vh;
  overflow: auto;
}
:where(.entry-content, .site-main) .subnav-issue-paper > ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
:where(.entry-content, .site-main) .subnav-issue-paper > ul > li {
  margin: 0 0 4px;
  padding: 0;
}
:where(.entry-content, .site-main) .subnav-issue-paper > ul > li > a {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
  display: block;
  padding: 0.75rem 1.125rem;
  position: relative;
}
:where(.entry-content, .site-main) .subnav-issue-paper > ul > li > a::before {
  background: var(--border-colour, transparent);
  content: "";
  inset: 0 auto 0 0;
  position: absolute;
  transition: background 0.2s ease-out;
  width: 4px;
}
:where(.entry-content, .site-main) .subnav-issue-paper > ul > li > a:hover, :where(.entry-content, .site-main) .subnav-issue-paper > ul > li > a:focus {
  text-decoration: underline;
}
:where(.entry-content, .site-main) .subnav-issue-paper > ul > li > a.current {
  --border-colour: #000;
}
:where(.entry-content, .site-main) .subnav-issue-paper .wp-block-buttons {
  align-items: flex-start;
  flex-direction: column;
  margin-top: 1.5rem;
}
@media (min-width: 700px) {
  :where(.entry-content, .site-main) .two-column-issue-layout {
    display: flex;
  }
  :where(.entry-content, .site-main) .issue-main-content {
    flex: 0 1 820px;
  }
  :where(.entry-content, .site-main) .issue-main-sidebar {
    flex: 0 0 308px;
    max-width: 308px;
  }
  :where(.entry-content, .site-main) .subnav-issue-paper {
    position: sticky;
    top: 0;
  }
}
:where(.entry-content, .site-main) .is-content-justification-space-between {
  justify-content: space-between;
}
:where(.entry-content, .site-main) .sort-by-field {
  align-items: center;
  display: flex;
  gap: 0.5rem;
  white-space: nowrap;
}
:where(.entry-content, .site-main) .sort-by-field label {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
}
:where(.entry-content, .site-main) .featured-item__label {
  text-transform: uppercase;
}
:where(.entry-content, .site-main) .featured-item__label:before {
  mask: url(../images/bulletin.svg) no-repeat center;
  background-color: currentColor;
  display: inline-block;
  content: '"';
  height: 18px;
  margin: 0 4px 0 0;
  position: relative;
  vertical-align: baseline;
  width: 18px;
}
:where(.entry-content, .site-main) .featured-item--snapshot .featured-item__label::before {
  mask-image: url(../images/snapshot.svg);
}
:where(.entry-content, .site-main) .featured-item--issue-paper .featured-item__label::before {
  mask-image: url(../images/issue-paper.svg);
}
:where(.entry-content, .site-main) .highcharts-container {
  width: auto !important;
}
:where(.entry-content, .site-main) .highcharts-root {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif !important;
}
:where(.entry-content, .site-main) .highcharts-root .highcharts-text-outline,
:where(.entry-content, .site-main) .highcharts-root tspan[style="font-weight: bold;"] {
  font-weight: 300 !important;
}
:where(.entry-content, .site-main) .highcharts-root tspan[style="font-style: italic;"] {
  font-weight: 600 !important;
  font-style: normal !important;
}
:where(.entry-content, .site-main) :is(.highcharts-yaxis-labels, .highcharts-yaxis-labels text, .highcharts-xaxis-labels, .highcharts-xaxis-labels text, .highcharts-axis-title, .highcharts-data-label tspan) {
  font-size: 14px !important;
  font-weight: 300 !important;
  line-height: 16px !important;
}
:where(.entry-content, .site-main) .highcharts-crosshair.highcharts-crosshair-category {
  stroke: rgba(121, 20, 78, 0.1) !important;
}

.author-page {
  padding-top: 2.5rem;
}

#listing {
  transition: opacity 0.3s ease-in;
}

#listing.listing-loading {
  opacity: 0.3;
  transition: opacity 0.1s ease-out;
}

.item-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  list-style: none;
  max-width: 75rem;
  padding: 0;
}
.item-cards > li {
  margin: 0;
}

.item-card {
  display: grid;
}
.item-card__title {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 24px;
  letter-spacing: 0;
  line-height: 28px;
}
.item-card a {
  border: 1px solid rgb(211.5, 233, 228);
  background: rgb(233.25, 244, 241.5);
  color: #000;
  display: grid;
  justify-content: center;
  align-items: center;
  min-height: 144px;
  padding: 1.5rem;
  text-align: center;
  transition: border-color 0.2s ease-out, background 0.2s ease-out, color 0.2s ease-out;
}
.item-card a:hover {
  border-color: rgb(185.4, 219.8, 211.8);
  background: rgb(211.5, 233, 228);
  color: #000;
}
.item-card--snapshot a {
  border-color: rgb(188, 137.5, 166.5);
  background: rgb(241.6, 231.5, 237.3);
}
.item-card--snapshot a:hover, .item-card--snapshot a:focus {
  border-color: rgb(147.8, 67, 113.4);
  background: rgb(228.2, 208, 219.6);
}

.tippy-box {
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.06), 0px 4px 6px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  background: #E8EBEB;
  border: 1px solid rgb(140.5, 140.5, 140.5);
  color: #000;
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
  max-width: 340px;
  padding: 8px 16px;
}
.tippy-box .tippy-content {
  padding: 0;
}
.tippy-box .tippy-arrow {
  display: none !important;
  z-index: 20;
}
.tippy-box .card__title {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
}
.tippy-box .card__title span {
  display: none;
}
.tippy-box[data-placement^=top] > .tippy-arrow:before {
  border-top-color: #C6BBDD;
  bottom: -8px;
}
.tippy-box[data-placement^=bottom] > .tippy-arrow:before {
  border-bottom-color: #C6BBDD;
  top: -8px;
}

.tooltip {
  background: none;
  border: 0;
  display: inline;
  color: currentColor;
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  margin: 0;
  padding: 0;
  text-decoration: underline;
  text-underline-offset: 3px;
  text-decoration-thickness: 1px;
  transition: all 0.1s;
  word-break: break-word;
}
.tooltip:hover, .tooltip:focus {
  text-decoration-thickness: 2px;
}

.tooltip:after {
  background: currentColor;
  mask-image: url(../images/glossary.svg);
  mask-repeat: no-repeat;
  color: currentColor;
  content: "";
  display: inline-block;
  height: 22px;
  margin: 0;
  padding: 0;
  position: relative;
  vertical-align: top;
  width: 10px;
}

input:where(:not([type=radio]):not([type=checkbox])),
select {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
  background: #FFF;
  border: 1px solid rgb(198.25, 212, 217.75);
  border-radius: 0;
  font-weight: 300;
  height: 40px;
  padding: 0.5rem 0.75rem;
  width: 100%;
}
input:where(:not([type=radio]):not([type=checkbox])):focus,
select:focus {
  box-shadow: 0 0 0 4px #f6be46, inset 0 0 0 2px #1a1a1a;
  outline: 4px solid transparent;
  outline-offset: 4px;
}

select {
  appearance: none;
  background-image: url(../images/drop-arrow.svg);
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 8px 6px;
  cursor: pointer;
  padding: 0 2em 0 6px;
}

label {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
}

.archive-form {
  display: flex;
  flex-wrap: wrap;
  margin: 40px auto 50px;
  max-width: none;
}
.archive-form__row {
  align-items: flex-start;
  flex: 1 1 100%;
  flex-wrap: wrap;
  display: flex;
  gap: 1.5rem;
  justify-content: space-between;
}
.archive-form__row--bottom {
  align-items: flex-end;
}
@media (max-width: 674px) {
  .archive-form__row {
    flex-direction: column;
  }
  .archive-form__row--bottom {
    align-items: flex-start;
  }
}
.archive-form__row + .archive-form__row:not(.archive-form__row--flush) {
  margin-top: 40px;
}
.archive-form__counter {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
  flex: 1 0 calc(100% - 220px);
  margin: 0;
}
.archive-form .sort-by {
  align-items: baseline;
  display: flex;
  gap: 0.75rem;
  white-space: nowrap;
}
.archive-form .sort-by select {
  min-width: 120px;
}
.archive-form label,
.archive-form legend {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
}
.archive-form fieldset {
  border: 0;
  flex: 1 1 auto;
  margin: 0;
  padding: 0;
}
.archive-form fieldset legend {
  float: left;
}
.archive-form fieldset legend + * {
  clear: both;
  margin-top: 0.75rem;
}
.archive-form > fieldset + * {
  clear: both;
  margin-top: 0.75rem;
}

.list-of-checkbox-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  list-style: none;
  margin: 0;
  padding: 0;
}
.list-of-checkbox-buttons > li {
  margin: 0;
  padding: 0;
}

.styled-checkbox,
.styled-radio {
  cursor: pointer;
  position: relative;
}
.styled-checkbox:hover input + span:before,
.styled-radio:hover input + span:before {
  box-shadow: 0 0 0 10px #b1b4b6;
}
.styled-checkbox input,
.styled-radio input {
  cursor: pointer;
  left: 0;
  height: 26px;
  opacity: 0;
  position: absolute;
  top: calc(50% - 13px);
  width: 26px;
  z-index: 2;
}
.styled-checkbox input:checked + span:after,
.styled-radio input:checked + span:after {
  opacity: 1;
}
.styled-checkbox input + span,
.styled-radio input + span {
  align-items: center;
  display: flex;
  gap: 0.75rem;
  margin-top: 0.75rem;
  position: relative;
}
.styled-checkbox input + span:before, .styled-checkbox input + span:after,
.styled-radio input + span:before,
.styled-radio input + span:after {
  border: solid 2px #1a1a1a;
  content: "";
  flex: 0 0 26px;
  left: 0;
  height: 26px;
  top: 0;
  width: 26px;
}
.styled-checkbox input + span:before,
.styled-radio input + span:before {
  background: #fff;
}
.styled-checkbox input + span:after,
.styled-radio input + span:after {
  border-right: 0;
  border-top: 0;
  height: 7px;
  left: 6px;
  opacity: 0.2;
  position: absolute;
  top: calc(50% - 3px);
  transform: rotateZ(-45deg);
  width: 15px;
}

.styled-radio input + span:before {
  border-radius: 50%;
  flex-basis: 20px;
  height: 20px;
  width: 20px;
}
.styled-radio input + span:after {
  background: #1a1a1a;
  border: 0;
  border-radius: 50%;
  height: 10px;
  left: 5px;
  top: 5px;
  width: 10px;
}

.styled-checkbox-button {
  cursor: pointer;
}
.styled-checkbox-button span {
  background: rgb(232.3, 237.8, 240.1);
  border: solid 1px rgb(94.7, 94.7, 94.7);
  border-radius: 1.5rem;
  display: block;
  font-style: normal;
  font-weight: 300;
  font-size: 13px;
  line-height: 13px;
  letter-spacing: 0.02em;
  padding: 8px 12px;
  white-space: nowrap;
  transition: background 0.2s ease;
}
.styled-checkbox-button input {
  clip: rect(0 0 0 0);
  height: 1px;
  overflow: hidden;
  position: absolute;
  width: 1px;
}
.styled-checkbox-button:hover span,
.styled-checkbox-button input:focus + span {
  background: rgb(141.5, 169, 180.5);
}
.styled-checkbox-button input:checked + span {
  background: #1c536a;
}

.option-select-container {
  align-items: flex-end;
  flex: 1 0 calc(100% - 300px) !important;
  min-width: 600px;
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  row-gap: 0;
}
@media (max-width: 622px) {
  .option-select-container {
    align-items: stretch;
    flex-direction: column;
    flex-wrap: nowrap;
    min-width: 0;
    width: 100%;
  }
  .option-select-container legend {
    flex: 0 0 auto !important;
  }
  .option-select-container .option-select-group {
    max-width: none;
  }
}
.option-select-container > * {
  flex: 0 1 30%;
  max-width: 250px;
}
.option-select-container legend {
  flex: 1 0 100%;
  max-width: none;
}

.search-by {
  margin: 0 0 0.5rem;
}

.option-select-group {
  margin: 0 0 0.5rem;
  min-width: 170px;
  padding: 0;
  position: relative;
  width: auto;
}
.option-select-group .options-trigger {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
  background: #FFF;
  border: 1px solid rgb(198.25, 212, 217.75);
  border-radius: 0;
  font-weight: 300;
  height: 40px;
  padding: 0.5rem 0.75rem;
  width: 100%;
  background: url(../images/drop-arrow.svg) no-repeat calc(100% - 16px) center;
  background-size: 8px 6px;
  cursor: pointer;
  display: block;
  padding: 0.5rem 0.75rem;
  text-align: left;
  transition: all 0.2s ease;
  width: 100%;
}
.option-select-group .options-trigger:focus {
  box-shadow: 0 0 0 4px #f6be46, inset 0 0 0 2px #1a1a1a;
  outline: 4px solid transparent;
  outline-offset: 4px;
}
.option-select-group .options-trigger:focus {
  box-shadow: 0 0 0 4px #f6be46;
  outline: 4px solid transparent;
  outline-offset: 4px;
}
.option-select-group .options-trigger:focus-visible {
  box-shadow: 0 0 0 4px #f6be46, inset 0 0 0 2px #1a1a1a;
  outline: 4px solid transparent;
  outline-offset: 4px;
}
.option-select-group .options-trigger[aria-expanded=true] {
  border-bottom-color: #fff;
  border-radius: 4px 4px 0 0;
}
.option-select-group .options-trigger:has(+ .js-selected-counter:not(:empty)) {
  padding-top: 6px;
  padding-bottom: 14px;
}
.option-select-group .option-select-group__title {
  margin: 0;
}
.option-select-group .options-container {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
  background: #FFF;
  border: 1px solid rgb(198.25, 212, 217.75);
  border-radius: 0;
  font-weight: 300;
  height: 40px;
  padding: 0.5rem 0.75rem;
  width: 100%;
  border-top-color: #fff;
  border-radius: 0 0 4px 4px;
  height: auto;
  max-width: 352px;
  padding: 0.75rem 1.125rem;
  position: absolute;
  right: 0;
  top: calc(100% - 1px);
  left: 0;
  z-index: 100;
}
.option-select-group .options-container:focus {
  box-shadow: 0 0 0 4px #f6be46, inset 0 0 0 2px #1a1a1a;
  outline: 4px solid transparent;
  outline-offset: 4px;
}
.option-select-group .options-container label {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
  font-weight: 300;
}
.option-select-group .list-of-checkboxes {
  align-content: stretch;
  display: flex;
  flex-direction: column;
  gap: 8px;
  list-style: none;
  margin: 0;
  padding: 0;
}
.option-select-group .list-of-checkboxes li {
  margin: 0;
  padding: 0;
}

@media (max-width: 940px) {
  .option-select-group .options-container {
    left: 0;
    right: auto;
  }
}
@media (max-width: 600px) {
  .option-select-group:last-child .options-container {
    left: auto;
    right: 0;
  }
}
@media (max-width: 431px) {
  .option-select-group .options-container {
    left: 0;
    right: 0;
    max-width: 100%;
    width: auto;
  }
}

#primary .gform_next_button {
  align-items: flex-start;
  background: var(--background, rgb(73.4, 117.4, 135.8));
  border-radius: 0;
  border: 0;
  color: #fff;
  cursor: pointer;
  display: inline-flex;
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 18px;
  padding: 0.75rem 2rem;
  text-align: center;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
}
#primary .gform_next_button:hover, #primary .gform_next_button:hover:visited, #primary .gform_next_button:focus {
  background: var(--background-hover, #1c536a);
  outline: none;
  text-decoration: underline;
}
#primary .gform_next_button:focus-visible {
  box-shadow: inset 0px 0px 0 2px #000;
  outline: 4px solid #f6be46 !important;
  outline-offset: 0;
}
#primary .gform_wrapper {
  --gf-label-space-tertiary: 0;
  margin-bottom: 2.5rem;
  margin-top: 2.5rem;
}
#primary .gform_wrapper .gfield_label {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 21px;
  letter-spacing: 0.02em;
  margin: 0;
}
#primary .gform_wrapper input[type=submit] {
  align-items: flex-start;
  background: var(--background, rgb(73.4, 117.4, 135.8));
  border-radius: 0;
  border: 0;
  color: #fff;
  cursor: pointer;
  display: inline-flex;
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 18px;
  padding: 0.75rem 2rem;
  text-align: center;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
}
#primary .gform_wrapper input[type=submit]:hover, #primary .gform_wrapper input[type=submit]:hover:visited, #primary .gform_wrapper input[type=submit]:focus {
  background: var(--background-hover, #1c536a);
  outline: none;
  text-decoration: underline;
}
#primary .gform_wrapper input[type=submit]:focus-visible {
  box-shadow: inset 0px 0px 0 2px #000;
  outline: 4px solid #f6be46 !important;
  outline-offset: 0;
}
#primary .gform_wrapper input:where(:not([type=radio]):not([type=checkbox]):not([type=submit])):not([type=button]),
#primary .gform_wrapper textarea,
#primary .gform_wrapper select {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
  background: #FFF;
  border: 1px solid rgb(198.25, 212, 217.75);
  border-radius: 0;
  font-weight: 300;
  height: 40px;
  padding: 0.5rem 0.75rem;
  width: 100%;
}
#primary .gform_wrapper input:where(:not([type=radio]):not([type=checkbox]):not([type=submit])):not([type=button]):focus,
#primary .gform_wrapper textarea:focus,
#primary .gform_wrapper select:focus {
  box-shadow: 0 0 0 4px #f6be46, inset 0 0 0 2px #1a1a1a;
  outline: 4px solid transparent;
  outline-offset: 4px;
}
#primary .gform-theme--framework .gfield--type-choice .gchoice,
#primary .gform-theme--framework .gfield--type-choice .ginput_container_consent {
  grid-template-columns: var(--gf-ctrl-choice-size) auto;
}

.archive-filter__pills {
  display: flex;
  justify-content: flex-start;
  gap: 0.75rem;
  margin: 0.75rem auto 40px;
}
.archive-filter__pills:empty {
  display: none;
}

.archive-filter {
  align-items: flex-end;
  row-gap: 0;
  column-gap: 20px;
  display: flex;
  flex-wrap: wrap;
  margin-top: 72px;
}
.archive-filter__section {
  flex: 0 1 28%;
}
.archive-filter__section--full {
  flex: 1 0 100%;
}

.main-archive-search-text {
  max-width: 320px;
}

.main-search-form {
  display: flex;
  gap: 4px;
}
.main-search-form__input {
  height: 42px;
}

.tabs {
  --wp--style--global--content-size: 51.25rem;
  background: #fff;
  border-left: 4px solid #1c536a;
  box-shadow: 0px 4px 12px 0px rgba(21, 55, 53, 0.15);
  max-width: 51.25rem;
  margin: 36px auto;
  padding: 36px;
}
.tabs__list {
  display: flex;
  gap: 0;
  list-style: none;
  margin: 0;
  padding: 0;
}
.tabs__list li {
  margin: 0 0 1.5rem;
  padding: 0;
}
.tabs__list li a {
  align-items: center;
  background: rgb(232.3, 237.8, 240.1);
  border: solid 1px #1c536a;
  color: rgb(14, 41.5, 53);
  display: flex;
  gap: 6px;
  padding: 2px 6px;
  text-decoration: none;
  transition: all 0.2s ease;
}
.tabs__list li a:hover {
  background: #1c536a;
  color: #fff;
}
.tabs__list li a[aria-selected=true] {
  background: #1c536a;
  color: #fff;
}
.tabs__list li:first-child a {
  border-right: none;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}
.tabs__list li:last-child a {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}
.tabs__link__icon {
  mask: url(../images/graph.svg) no-repeat center center;
  background: currentColor;
  display: block;
  height: 24px;
  width: 24px;
}
.tabs__link__icon--table {
  mask: url(../images/table.svg) no-repeat center center;
}
.tabs__panel {
  height: 0;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  transition: opacity 0.3s ease, visibility 0.3s ease, height 0.3s ease 0.3s;
  visibility: hidden;
}
.tabs__panel[aria-hidden=false] {
  height: auto;
  opacity: 1;
  overflow: auto;
  position: relative;
  transition: opacity 0.3s ease, visibility 0.3s ease, height 0.01s ease;
  visibility: visible;
}
.tabs__panel.fullscreen-view {
  background: #fff;
  display: flex;
  flex-direction: column;
  inset: 0;
  height: 100vh;
  height: 100dvh;
  position: fixed;
  z-index: 100000;
}
.tabs__panel.fullscreen-view .tabs__content {
  flex: 1 1 auto;
  overflow: auto;
}
.tabs__panel.fullscreen-view .shrink {
  display: block;
}
.tabs__panel.fullscreen-view .expand {
  display: none;
}
.tabs__content__actions {
  background: #fff;
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  padding: 0.75rem 1.125rem;
}
.tabs__content__actions a,
.tabs__content__actions button {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
  align-items: center;
  background: #fff;
  border: solid 1px #1c536a;
  border-radius: 2px;
  display: flex;
  color: rgb(209.6, 220.6, 225.2);
  cursor: pointer;
  gap: 12px;
  padding: 4px;
  text-decoration: none;
  transition: all 0.2s ease;
}
.tabs__content__actions a:hover, .tabs__content__actions a:focus,
.tabs__content__actions button:hover,
.tabs__content__actions button:focus {
  background: rgb(209.6, 220.6, 225.2);
  color: rgb(73.4, 117.4, 135.8);
}
.tabs__content__actions svg {
  display: block;
  height: 24px;
  width: 24px;
}
.tabs__content__actions .shrink {
  display: none;
}
.tabs .flourish-embed iframe {
  display: block;
}
.tabs .flourish-credit {
  display: none !important;
  position: absolute !important;
}

.tabs--tabless .tabs__list {
  display: none;
}

.breadcrumbs {
  background: var(--bg-colour, rgb(246.3, 250.6, 249.6));
  color: #193b39;
  clear: both;
  font-weight: 300;
  font-size: 14px;
  line-height: 18px;
  margin: 0 !important;
  max-width: 100% !important;
  width: 100% !important;
}
.breadcrumbs :where(ol, ul) {
  display: flex;
  list-style: none;
  margin: 0 auto;
  padding: 0.75rem 0;
  overflow: hidden;
  overflow-x: auto;
  white-space: nowrap;
  width: calc(100% - var(--side-gutter) * 2);
}
.breadcrumbs li {
  flex: 0 1 0;
  margin: 0;
  padding: 0;
}
.breadcrumbs li:not(:first-child)::before {
  background: url(../images/crumb.svg) 50% 50% no-repeat;
  color: #858788;
  display: inline-block;
  content: "";
  height: 9px;
  width: 9px;
  margin: 0 12px;
  padding: 0;
  vertical-align: middle;
}
.breadcrumbs li:last-child {
  flex: 1 1 auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 400px;
}
.breadcrumbs a, .breadcrumbs span {
  color: #193b39;
  display: inline-block;
  font-size: 13px;
  min-width: 30px;
  padding: 0 0 2px;
}
.breadcrumbs a {
  color: #1a1a1a;
  text-decoration: underline;
}
.breadcrumbs a:hover, .breadcrumbs a:focus {
  text-decoration: none;
}

:is(#primary, .is-root-container) :is(.wpdtSimpleTable, .table, .wp-block-table > table) {
  border: 1px solid rgb(228.2, 208, 219.6);
  background: rgb(241.6, 231.5, 237.3);
  border-collapse: collapse;
}
:is(#primary, .is-root-container) :is(.wpdtSimpleTable, .table, .wp-block-table > table) :is(tbody tr td, thead tr td, tbody tr th, thead tr th) {
  border-color: rgb(228.2, 208, 219.6);
}
:is(#primary, .is-root-container) :is(.wpdtSimpleTable, .table, .wp-block-table > table) :is(td, th):is([class*=wpdt-bc-]) {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
  font-weight: 700;
}
:is(#primary, .is-root-container) .wpdt-c.wpDataTableContainerSimpleTable.wdtscroll table.wpdtSimpleTable {
  min-width: 100%;
}

.wpDataTableContainerSimpleTable {
  overflow-x: auto;
}

.pagination ul {
  align-items: center;
  display: flex;
  gap: 4px;
  justify-content: center;
  list-style: none;
  margin-bottom: 0;
  margin-top: 0;
  padding: 0;
}
.pagination li {
  margin: 0;
  padding: 0 0.5rem;
}
.pagination a:not(.next-prev) {
  display: block;
  padding: 0;
}
.pagination a.next-prev {
  background-size: 0;
}
.pagination .current {
  font-weight: bold;
}

.cookie-consent {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
  background: rgb(209.6, 220.6, 225.2) !important;
  border: 0 solid #1c536a;
  border-left-width: 4px;
  box-shadow: 0px 4px 8px rgba(21, 55, 53, 0.15);
  bottom: 0;
  color: #1a1a1a;
  display: none !important;
  left: 16px;
  padding: 32px 16px;
  position: fixed !important;
  text-align: center;
  max-width: 450px;
  width: calc(100vw - 38px);
  z-index: 99;
}
.cookie-consent :is(button, a) {
  color: #1a1a1a;
}
.cookie-consent .button {
  align-items: flex-start;
  background: var(--background, rgb(73.4, 117.4, 135.8));
  border-radius: 0;
  border: 0;
  color: #fff;
  cursor: pointer;
  display: inline-flex;
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 18px;
  padding: 0.75rem 2rem;
  text-align: center;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
  cursor: default;
}
.cookie-consent .button:hover, .cookie-consent .button:hover:visited, .cookie-consent .button:focus {
  background: var(--background-hover, #1c536a);
  outline: none;
  text-decoration: underline;
}
.cookie-consent .button:focus-visible {
  box-shadow: inset 0px 0px 0 2px #000;
  outline: 4px solid #f6be46 !important;
  outline-offset: 0;
}
.cookie-consent .base-card__text {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
}

@media print {
  .top-bar,
  .main-header,
  .breadcrumbs,
  .main-footer,
  .in-page-sub-nav,
  .social-sharing {
    display: none !important;
  }
  body {
    background: #fff;
  }
}
/* Slider */
.slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  user-select: none;
  -webkit-touch-callout: none;
  -khtml-user-select: none;
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
}

.slick-list {
  position: relative;
  display: block;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.slick-list:focus {
  outline: none;
}

.slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.slick-slider .slick-track,
.slick-slider .slick-list {
  transform: translate3d(0, 0, 0);
}

.slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
  max-width: none !important;
}

.slick-track:before,
.slick-track:after {
  display: table;
  content: "";
}

.slick-track:after {
  clear: both;
}

.slick-loading .slick-track {
  visibility: hidden;
}

.slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

[dir=rtl] .slick-slide {
  float: right;
}

.slick-slide img {
  display: block;
}

.slick-slide.slick-loading img {
  display: none;
}

.slick-slide.dragging img {
  pointer-events: none;
}

.slick-initialized .slick-slide {
  display: block;
}

.slick-loading .slick-slide {
  visibility: hidden;
}

.slick-vertical .slick-slide {
  display: block;
  height: auto;
  border: 1px solid transparent;
}

.slick-arrow.slick-hidden {
  display: none;
}

.slick-slider {
  margin-top: 1.5rem;
  width: calc(100% - 100px);
}

.slick-list .slick-track {
  align-items: stretch;
  display: flex;
  gap: 1.5rem;
}
.slick-list .slick-track > * {
  flex: 1 0 0;
}
.slick-list .slick-track .slick-slide {
  height: auto;
}

.slick-prev,
.slick-next {
  background: none;
  border: none;
  color: #1c536a;
  cursor: pointer;
  margin: 0;
  padding: 0;
  position: absolute;
  right: 100%;
  top: calc(50% - 17px);
  width: 40px !important;
  transition: color 0.2s ease-in-out;
}
.slick-prev:hover, .slick-prev:focus,
.slick-next:hover,
.slick-next:focus {
  color: rgb(25.2, 74.7, 95.4);
}
.slick-prev svg,
.slick-next svg {
  display: block;
  height: 40px !important;
  width: 40px !important;
}

.slick-next {
  left: 100%;
  right: auto;
}

.slick-disabled {
  opacity: 0.1;
  pointer-events: none;
}

@media (max-width: 740px) {
  .slick-slider {
    margin-bottom: 80px;
    width: calc(100% - 3rem);
  }
  .slick-list .slick-track {
    gap: 0;
  }
  .slick-prev,
  .slick-next {
    left: 50%;
    right: auto;
    top: calc(100% + 0.75rem);
    transform: translate(-50%, 0);
  }
  .slick-prev {
    margin-left: -36px;
  }
  .slick-next {
    margin-left: 36px;
  }
}
.main-footer {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
  font-weight: 300;
  background: #000;
  color: #fff;
  padding: 64px 0 40px;
  z-index: 100;
}
.main-footer a:not(.wp-block-button__link) {
  color: #fff;
}
.main-footer a.wp-block-button__link {
  padding-bottom: 10px;
  padding-top: 10px;
}
.main-footer .wp-block-group {
  gap: 2.5rem;
}
@media (max-width: 800px) {
  .main-footer .wp-block-group.is-content-justification-space-between {
    flex-direction: column;
    align-items: stretch;
  }
  .main-footer .wp-block-group.is-content-justification-right {
    gap: 0.75rem;
    flex-wrap: wrap;
    justify-content: flex-start;
  }
  .main-footer .wp-block-group.is-content-justification-right > p {
    margin: 0;
  }
}
.main-footer .wp-block-group .wp-block-social-links {
  margin-left: auto;
}
.main-footer ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.main-footer ul li {
  margin: 0 0 0.75rem;
  padding: 0;
}
.main-footer ul li ul {
  list-style: disc;
  margin: 0.75rem 0 0 1.125rem;
}
.main-footer p {
  margin-bottom: 0.75rem;
  margin-top: 0.75rem;
}
.main-footer .wp-block-separator:not(.is-style-wide):not(.is-style-dots) {
  border-color: rgb(198.25, 212, 217.75);
  width: 100%;
}

.main-footer-container {
  margin: 0 auto;
  max-width: 75rem;
  width: calc(100% - var(--side-gutter) * 2);
}

.main-footer .wp-block-buttons {
  margin-bottom: 1.5rem;
}
.main-footer .wp-block-social-links {
  gap: 0.5rem;
  justify-content: flex-start;
}
.main-footer a.wp-block-button__link.has-dias-white-background-color,
.main-footer .wp-block-social-links:not(.is-style-logos-only) .wp-social-link {
  background: #fff;
  color: rgb(73.4, 117.4, 135.8);
}
.main-footer a.wp-block-button__link.has-dias-white-background-color:hover, .main-footer a.wp-block-button__link.has-dias-white-background-color:focus,
.main-footer .wp-block-social-links:not(.is-style-logos-only) .wp-social-link:hover,
.main-footer .wp-block-social-links:not(.is-style-logos-only) .wp-social-link:focus {
  background: rgb(246.3, 250.6, 249.6);
  color: #1c536a;
}

:root {
  --width: 520px;
}

.related-content-container {
  padding: 40px 0 112px;
}
.related-content-container > h2 {
  margin-bottom: 1.5em;
}

.js-filter-item {
  position: relative;
  transition: visibility 0.2s ease-out, opacity 0.2s ease-out, transform 0.3s ease-out;
  z-index: 20;
}
.js-filter-item > * {
  transition: all 0.2s ease-in-out, box-shadow 10ms 3s;
}
.js-filter-item.no-transitions {
  transition: none !important;
}
.js-filter-item.filter-item-hidden-on-load {
  opacity: 0 !important;
  position: absolute;
}
.js-filter-item.filter-item-hidden {
  width: 100%;
  min-height: var(--height);
  max-width: var(--width);
  opacity: 0 !important;
  position: absolute;
  visibility: hidden !important;
  transition: none !important;
  transform: translate3d(0, -10px, 0);
  z-index: 1;
}
.js-filter-item.filter-item-hidden > * {
  box-shadow: none !important;
  transition: none !important;
}

.anchor-line {
  right: 200vw;
  position: absolute;
  top: 0;
  width: 12px;
  z-index: 10000000000000;
}
.anchor-line__section {
  background: rgba(255, 255, 0, 0.5);
  position: absolute;
  width: 100%;
}
.anchor-line__section:nth-of-type(2n) {
  background: rgba(255, 0, 0, 0.5);
}