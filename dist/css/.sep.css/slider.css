@font-face {
  font-family: "Zosia";
  src: url("../fonts/Zosia-Display.woff2") format("woff2");
  font-style: normal;
  font-weight: 400;
}
@font-face {
  font-family: "Averta";
  src: url("../fonts/Averta-Semibold.woff2") format("woff2");
  font-style: normal;
  font-weight: 600;
}
@font-face {
  font-family: "Averta";
  src: url("../fonts/Averta-Regular.woff2") format("woff2");
  font-style: normal;
  font-weight: 400;
}
@font-face {
  font-family: "Averta";
  src: url("../fonts/Averta-Bold.woff2") format("woff2");
  font-style: normal;
  font-weight: 700;
}
@font-face {
  font-family: "Averta";
  src: url("../fonts/Averta-Extrathin.woff2") format("woff2");
  font-style: normal;
  font-weight: 200;
}
@font-face {
  font-family: "Averta";
  src: url("../fonts/Averta-SemiboldItalic.woff2") format("woff2");
  font-style: italic;
  font-weight: 600;
}
@font-face {
  font-family: "Averta";
  src: url("../fonts/Averta-RegularItalic.woff2") format("woff2");
  font-style: italic;
  font-weight: 400;
}
@font-face {
  font-family: "Averta";
  src: url("../fonts/Averta-ExtraBold.woff2") format("woff2");
  font-style: normal;
  font-weight: 800;
}
@font-face {
  font-family: "Averta";
  src: url("../fonts/Averta-Thin.woff2") format("woff2");
  font-style: normal;
  font-weight: 300;
}
.cards-event-title-text {
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 24px;
  letter-spacing: -0.02em;
}

.inline-block {
  display: inline-block;
}

.container {
  margin-left: auto;
  margin-right: auto;
  max-width: 78rem;
  width: calc(100% - 2.25rem);
}

@media (max-width: 600px) {
  .hidden-accessible-mob {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
    word-wrap: normal !important;
  }
}
.custom-scrollbar {
  --scrollbarBG: #fff;
  --thumbBG: #0F294A;
  padding-right: 2px;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar {
  --scrollbarBG: #fff;
  --thumbBG: #0F294A;
}
.custom-scrollbar::-webkit-scrollbar {
  -webkit-appearance: none;
  background-color: var(--scrollbarBG);
  height: 4px;
  width: 4px;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: var(--thumbBG);
}

.range-slider-box {
  transform: translateX(12px);
}

.theme-blueprint {
  --panel-bg: #E7E2DE;
  --panel-bg-hover: #E7E2DE;
  --panel-bg-fill: #F6A4B7;
  --panel-bg-border-radius: 12px;
  --pointer-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  --pointer-height: 24px;
  --pointer-width: 24px;
  --pointer-border: 1px;
}

.theme-blueprint.range-slider {
  max-width: calc(100% - 24px);
}

.theme-blueprint .panel,
.theme-blueprint .panel-fill,
.theme-blueprint .pointer-shape {
  border: 0.5px solid #646363;
}
.theme-blueprint .tooltip {
  background: none !important;
  border: none !important;
  color: #000 !important;
  text-align: center !important;
  font-family: "Averta", sans-serif !important;
  font-size: 12px !important;
  font-style: normal !important;
  font-weight: 500 !important;
  height: 24px !important;
  line-height: 24px !important;
  pointer-events: none !important;
  transform: translate(-50%, 0) !important;
  z-index: 300 !important;
}
.theme-blueprint .tooltips {
  margin: 0 -12px;
  position: absolute;
  width: calc(100% + 24px);
}
.theme-blueprint .tooltips + .container {
  margin: 0 -12px;
  width: calc(100% + 24px);
}
.theme-blueprint .pointer-0 {
  transform: translate(0%, -50%) !important;
}
.theme-blueprint .pointer-0 .pointer-shape {
  transform: translate(-2px, 0);
}
.theme-blueprint .pointer-1 {
  transform: translate(-100%, -50%) !important;
}
.theme-blueprint .pointer-1 .pointer-shape {
  transform: translate(1px, 0);
}
.theme-blueprint .tooltip-1 {
  transform: translate(-2px, 0.5px) !important;
}
.theme-blueprint .tooltip-2 {
  transform: translate(-23px, 0.5px) !important;
}