.wp-block-button__link {
  align-content: center;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  height: 100%;
  text-align: center;
  width: 100%;
  word-break: break-word;
}

.wp-block-button__link.aligncenter {
  text-align: center;
}

.wp-block-button__link.alignright {
  text-align: right;
}

:where(.wp-block-button__link) {
  border-radius: 9999px;
  box-shadow: none;
  padding: calc(0.667em + 2px) calc(1.333em + 2px);
  text-decoration: none;
}

.wp-block-button[style*=text-decoration] .wp-block-button__link {
  text-decoration: inherit;
}

.wp-block-buttons > .wp-block-button.has-custom-width {
  max-width: none;
}

.wp-block-buttons > .wp-block-button.has-custom-width .wp-block-button__link {
  width: 100%;
}

.wp-block-buttons > .wp-block-button.has-custom-font-size .wp-block-button__link {
  font-size: inherit;
}

.wp-block-buttons > .wp-block-button.wp-block-button__width-25 {
  width: calc(25% - var(--wp--style--block-gap, 0.5em) * 0.75);
}

.wp-block-buttons > .wp-block-button.wp-block-button__width-50 {
  width: calc(50% - var(--wp--style--block-gap, 0.5em) * 0.5);
}

.wp-block-buttons > .wp-block-button.wp-block-button__width-75 {
  width: calc(75% - var(--wp--style--block-gap, 0.5em) * 0.25);
}

.wp-block-buttons > .wp-block-button.wp-block-button__width-100 {
  flex-basis: 100%;
  width: 100%;
}

.wp-block-buttons.is-vertical > .wp-block-button.wp-block-button__width-25 {
  width: 25%;
}

.wp-block-buttons.is-vertical > .wp-block-button.wp-block-button__width-50 {
  width: 50%;
}

.wp-block-buttons.is-vertical > .wp-block-button.wp-block-button__width-75 {
  width: 75%;
}

.wp-block-button.is-style-squared, .wp-block-button__link.wp-block-button.is-style-squared {
  border-radius: 0;
}

.wp-block-button.no-border-radius, .wp-block-button__link.no-border-radius {
  border-radius: 0 !important;
}

:root :where(.wp-block-button .wp-block-button__link.is-style-outline), :root :where(.wp-block-button.is-style-outline > .wp-block-button__link) {
  border: 2px solid;
  padding: 0.667em 1.333em;
}

:root :where(.wp-block-button .wp-block-button__link.is-style-outline:not(.has-text-color)), :root :where(.wp-block-button.is-style-outline > .wp-block-button__link:not(.has-text-color)) {
  color: currentColor;
}

:root :where(.wp-block-button .wp-block-button__link.is-style-outline:not(.has-background)), :root :where(.wp-block-button.is-style-outline > .wp-block-button__link:not(.has-background)) {
  background-color: initial;
  background-image: none;
}

.wp-block-buttons {
  box-sizing: border-box;
}

.wp-block-buttons.is-vertical {
  flex-direction: column;
}

.wp-block-buttons.is-vertical > .wp-block-button:last-child {
  margin-bottom: 0;
}

.wp-block-buttons > .wp-block-button {
  display: inline-block;
  margin: 0;
}

.wp-block-buttons.is-content-justification-left {
  justify-content: flex-start;
}

.wp-block-buttons.is-content-justification-left.is-vertical {
  align-items: flex-start;
}

.wp-block-buttons.is-content-justification-center {
  justify-content: center;
}

.wp-block-buttons.is-content-justification-center.is-vertical {
  align-items: center;
}

.wp-block-buttons.is-content-justification-right {
  justify-content: flex-end;
}

.wp-block-buttons.is-content-justification-right.is-vertical {
  align-items: flex-end;
}

.wp-block-buttons.is-content-justification-space-between {
  justify-content: space-between;
}

.wp-block-buttons.aligncenter {
  text-align: center;
}

.wp-block-buttons:not(.is-content-justification-space-between, .is-content-justification-right, .is-content-justification-left, .is-content-justification-center) .wp-block-button.aligncenter {
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

.wp-block-buttons[style*=text-decoration] .wp-block-button, .wp-block-buttons[style*=text-decoration] .wp-block-button__link {
  text-decoration: inherit;
}

.wp-block-buttons.has-custom-font-size .wp-block-button__link {
  font-size: inherit;
}

.wp-block-button.aligncenter {
  text-align: center;
}

.wp-block-code {
  box-sizing: border-box;
}

.wp-block-code code {
  direction: ltr;
  display: block;
  font-family: inherit;
  overflow-wrap: break-word;
  text-align: initial;
  white-space: pre-wrap;
}

.wp-block-columns {
  align-items: normal !important;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap !important;
}

@media (min-width: 782px) {
  .wp-block-columns {
    flex-wrap: nowrap !important;
  }
}
.wp-block-columns.are-vertically-aligned-top {
  align-items: flex-start;
}

.wp-block-columns.are-vertically-aligned-center {
  align-items: center;
}

.wp-block-columns.are-vertically-aligned-bottom {
  align-items: flex-end;
}

@media (max-width: 781px) {
  .wp-block-columns:not(.is-not-stacked-on-mobile) > .wp-block-column {
    flex-basis: 100% !important;
  }
}
@media (min-width: 782px) {
  .wp-block-columns:not(.is-not-stacked-on-mobile) > .wp-block-column {
    flex-basis: 0;
    flex-grow: 1;
  }
  .wp-block-columns:not(.is-not-stacked-on-mobile) > .wp-block-column[style*=flex-basis] {
    flex-grow: 0;
  }
}
.wp-block-columns.is-not-stacked-on-mobile {
  flex-wrap: nowrap !important;
}

.wp-block-columns.is-not-stacked-on-mobile > .wp-block-column {
  flex-basis: 0;
  flex-grow: 1;
}

.wp-block-columns.is-not-stacked-on-mobile > .wp-block-column[style*=flex-basis] {
  flex-grow: 0;
}

:where(.wp-block-columns) {
  margin-bottom: 1.75em;
}

:where(.wp-block-columns.has-background) {
  padding: 1.25em 2.375em;
}

.wp-block-column {
  flex-grow: 1;
  min-width: 0;
  overflow-wrap: break-word;
  word-break: break-word;
}

.wp-block-column.is-vertically-aligned-top {
  align-self: flex-start;
}

.wp-block-column.is-vertically-aligned-center {
  align-self: center;
}

.wp-block-column.is-vertically-aligned-bottom {
  align-self: flex-end;
}

.wp-block-column.is-vertically-aligned-stretch {
  align-self: stretch;
}

.wp-block-column.is-vertically-aligned-bottom, .wp-block-column.is-vertically-aligned-center, .wp-block-column.is-vertically-aligned-top {
  width: 100%;
}

.wp-block-cover, .wp-block-cover-image {
  align-items: center;
  background-position: 50%;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  min-height: 430px;
  overflow: hidden;
  overflow: clip;
  padding: 1em;
  position: relative;
}

.wp-block-cover .has-background-dim:not([class*=-background-color]), .wp-block-cover-image .has-background-dim:not([class*=-background-color]), .wp-block-cover-image.has-background-dim:not([class*=-background-color]), .wp-block-cover.has-background-dim:not([class*=-background-color]) {
  background-color: #000;
}

.wp-block-cover .has-background-dim.has-background-gradient, .wp-block-cover-image .has-background-dim.has-background-gradient {
  background-color: initial;
}

.wp-block-cover-image.has-background-dim:before, .wp-block-cover.has-background-dim:before {
  background-color: inherit;
  content: "";
}

.wp-block-cover .wp-block-cover__background, .wp-block-cover .wp-block-cover__gradient-background, .wp-block-cover-image .wp-block-cover__background, .wp-block-cover-image .wp-block-cover__gradient-background, .wp-block-cover-image.has-background-dim:not(.has-background-gradient):before, .wp-block-cover.has-background-dim:not(.has-background-gradient):before {
  bottom: 0;
  left: 0;
  opacity: 0.5;
  position: absolute;
  right: 0;
  top: 0;
}

.wp-block-cover-image.has-background-dim.has-background-dim-10 .wp-block-cover__background, .wp-block-cover-image.has-background-dim.has-background-dim-10 .wp-block-cover__gradient-background, .wp-block-cover-image.has-background-dim.has-background-dim-10:not(.has-background-gradient):before, .wp-block-cover.has-background-dim.has-background-dim-10 .wp-block-cover__background, .wp-block-cover.has-background-dim.has-background-dim-10 .wp-block-cover__gradient-background, .wp-block-cover.has-background-dim.has-background-dim-10:not(.has-background-gradient):before {
  opacity: 0.1;
}

.wp-block-cover-image.has-background-dim.has-background-dim-20 .wp-block-cover__background, .wp-block-cover-image.has-background-dim.has-background-dim-20 .wp-block-cover__gradient-background, .wp-block-cover-image.has-background-dim.has-background-dim-20:not(.has-background-gradient):before, .wp-block-cover.has-background-dim.has-background-dim-20 .wp-block-cover__background, .wp-block-cover.has-background-dim.has-background-dim-20 .wp-block-cover__gradient-background, .wp-block-cover.has-background-dim.has-background-dim-20:not(.has-background-gradient):before {
  opacity: 0.2;
}

.wp-block-cover-image.has-background-dim.has-background-dim-30 .wp-block-cover__background, .wp-block-cover-image.has-background-dim.has-background-dim-30 .wp-block-cover__gradient-background, .wp-block-cover-image.has-background-dim.has-background-dim-30:not(.has-background-gradient):before, .wp-block-cover.has-background-dim.has-background-dim-30 .wp-block-cover__background, .wp-block-cover.has-background-dim.has-background-dim-30 .wp-block-cover__gradient-background, .wp-block-cover.has-background-dim.has-background-dim-30:not(.has-background-gradient):before {
  opacity: 0.3;
}

.wp-block-cover-image.has-background-dim.has-background-dim-40 .wp-block-cover__background, .wp-block-cover-image.has-background-dim.has-background-dim-40 .wp-block-cover__gradient-background, .wp-block-cover-image.has-background-dim.has-background-dim-40:not(.has-background-gradient):before, .wp-block-cover.has-background-dim.has-background-dim-40 .wp-block-cover__background, .wp-block-cover.has-background-dim.has-background-dim-40 .wp-block-cover__gradient-background, .wp-block-cover.has-background-dim.has-background-dim-40:not(.has-background-gradient):before {
  opacity: 0.4;
}

.wp-block-cover-image.has-background-dim.has-background-dim-50 .wp-block-cover__background, .wp-block-cover-image.has-background-dim.has-background-dim-50 .wp-block-cover__gradient-background, .wp-block-cover-image.has-background-dim.has-background-dim-50:not(.has-background-gradient):before, .wp-block-cover.has-background-dim.has-background-dim-50 .wp-block-cover__background, .wp-block-cover.has-background-dim.has-background-dim-50 .wp-block-cover__gradient-background, .wp-block-cover.has-background-dim.has-background-dim-50:not(.has-background-gradient):before {
  opacity: 0.5;
}

.wp-block-cover-image.has-background-dim.has-background-dim-60 .wp-block-cover__background, .wp-block-cover-image.has-background-dim.has-background-dim-60 .wp-block-cover__gradient-background, .wp-block-cover-image.has-background-dim.has-background-dim-60:not(.has-background-gradient):before, .wp-block-cover.has-background-dim.has-background-dim-60 .wp-block-cover__background, .wp-block-cover.has-background-dim.has-background-dim-60 .wp-block-cover__gradient-background, .wp-block-cover.has-background-dim.has-background-dim-60:not(.has-background-gradient):before {
  opacity: 0.6;
}

.wp-block-cover-image.has-background-dim.has-background-dim-70 .wp-block-cover__background, .wp-block-cover-image.has-background-dim.has-background-dim-70 .wp-block-cover__gradient-background, .wp-block-cover-image.has-background-dim.has-background-dim-70:not(.has-background-gradient):before, .wp-block-cover.has-background-dim.has-background-dim-70 .wp-block-cover__background, .wp-block-cover.has-background-dim.has-background-dim-70 .wp-block-cover__gradient-background, .wp-block-cover.has-background-dim.has-background-dim-70:not(.has-background-gradient):before {
  opacity: 0.7;
}

.wp-block-cover-image.has-background-dim.has-background-dim-80 .wp-block-cover__background, .wp-block-cover-image.has-background-dim.has-background-dim-80 .wp-block-cover__gradient-background, .wp-block-cover-image.has-background-dim.has-background-dim-80:not(.has-background-gradient):before, .wp-block-cover.has-background-dim.has-background-dim-80 .wp-block-cover__background, .wp-block-cover.has-background-dim.has-background-dim-80 .wp-block-cover__gradient-background, .wp-block-cover.has-background-dim.has-background-dim-80:not(.has-background-gradient):before {
  opacity: 0.8;
}

.wp-block-cover-image.has-background-dim.has-background-dim-90 .wp-block-cover__background, .wp-block-cover-image.has-background-dim.has-background-dim-90 .wp-block-cover__gradient-background, .wp-block-cover-image.has-background-dim.has-background-dim-90:not(.has-background-gradient):before, .wp-block-cover.has-background-dim.has-background-dim-90 .wp-block-cover__background, .wp-block-cover.has-background-dim.has-background-dim-90 .wp-block-cover__gradient-background, .wp-block-cover.has-background-dim.has-background-dim-90:not(.has-background-gradient):before {
  opacity: 0.9;
}

.wp-block-cover-image.has-background-dim.has-background-dim-100 .wp-block-cover__background, .wp-block-cover-image.has-background-dim.has-background-dim-100 .wp-block-cover__gradient-background, .wp-block-cover-image.has-background-dim.has-background-dim-100:not(.has-background-gradient):before, .wp-block-cover.has-background-dim.has-background-dim-100 .wp-block-cover__background, .wp-block-cover.has-background-dim.has-background-dim-100 .wp-block-cover__gradient-background, .wp-block-cover.has-background-dim.has-background-dim-100:not(.has-background-gradient):before {
  opacity: 1;
}

.wp-block-cover .wp-block-cover__background.has-background-dim.has-background-dim-0, .wp-block-cover .wp-block-cover__gradient-background.has-background-dim.has-background-dim-0, .wp-block-cover-image .wp-block-cover__background.has-background-dim.has-background-dim-0, .wp-block-cover-image .wp-block-cover__gradient-background.has-background-dim.has-background-dim-0 {
  opacity: 0;
}

.wp-block-cover .wp-block-cover__background.has-background-dim.has-background-dim-10, .wp-block-cover .wp-block-cover__gradient-background.has-background-dim.has-background-dim-10, .wp-block-cover-image .wp-block-cover__background.has-background-dim.has-background-dim-10, .wp-block-cover-image .wp-block-cover__gradient-background.has-background-dim.has-background-dim-10 {
  opacity: 0.1;
}

.wp-block-cover .wp-block-cover__background.has-background-dim.has-background-dim-20, .wp-block-cover .wp-block-cover__gradient-background.has-background-dim.has-background-dim-20, .wp-block-cover-image .wp-block-cover__background.has-background-dim.has-background-dim-20, .wp-block-cover-image .wp-block-cover__gradient-background.has-background-dim.has-background-dim-20 {
  opacity: 0.2;
}

.wp-block-cover .wp-block-cover__background.has-background-dim.has-background-dim-30, .wp-block-cover .wp-block-cover__gradient-background.has-background-dim.has-background-dim-30, .wp-block-cover-image .wp-block-cover__background.has-background-dim.has-background-dim-30, .wp-block-cover-image .wp-block-cover__gradient-background.has-background-dim.has-background-dim-30 {
  opacity: 0.3;
}

.wp-block-cover .wp-block-cover__background.has-background-dim.has-background-dim-40, .wp-block-cover .wp-block-cover__gradient-background.has-background-dim.has-background-dim-40, .wp-block-cover-image .wp-block-cover__background.has-background-dim.has-background-dim-40, .wp-block-cover-image .wp-block-cover__gradient-background.has-background-dim.has-background-dim-40 {
  opacity: 0.4;
}

.wp-block-cover .wp-block-cover__background.has-background-dim.has-background-dim-50, .wp-block-cover .wp-block-cover__gradient-background.has-background-dim.has-background-dim-50, .wp-block-cover-image .wp-block-cover__background.has-background-dim.has-background-dim-50, .wp-block-cover-image .wp-block-cover__gradient-background.has-background-dim.has-background-dim-50 {
  opacity: 0.5;
}

.wp-block-cover .wp-block-cover__background.has-background-dim.has-background-dim-60, .wp-block-cover .wp-block-cover__gradient-background.has-background-dim.has-background-dim-60, .wp-block-cover-image .wp-block-cover__background.has-background-dim.has-background-dim-60, .wp-block-cover-image .wp-block-cover__gradient-background.has-background-dim.has-background-dim-60 {
  opacity: 0.6;
}

.wp-block-cover .wp-block-cover__background.has-background-dim.has-background-dim-70, .wp-block-cover .wp-block-cover__gradient-background.has-background-dim.has-background-dim-70, .wp-block-cover-image .wp-block-cover__background.has-background-dim.has-background-dim-70, .wp-block-cover-image .wp-block-cover__gradient-background.has-background-dim.has-background-dim-70 {
  opacity: 0.7;
}

.wp-block-cover .wp-block-cover__background.has-background-dim.has-background-dim-80, .wp-block-cover .wp-block-cover__gradient-background.has-background-dim.has-background-dim-80, .wp-block-cover-image .wp-block-cover__background.has-background-dim.has-background-dim-80, .wp-block-cover-image .wp-block-cover__gradient-background.has-background-dim.has-background-dim-80 {
  opacity: 0.8;
}

.wp-block-cover .wp-block-cover__background.has-background-dim.has-background-dim-90, .wp-block-cover .wp-block-cover__gradient-background.has-background-dim.has-background-dim-90, .wp-block-cover-image .wp-block-cover__background.has-background-dim.has-background-dim-90, .wp-block-cover-image .wp-block-cover__gradient-background.has-background-dim.has-background-dim-90 {
  opacity: 0.9;
}

.wp-block-cover .wp-block-cover__background.has-background-dim.has-background-dim-100, .wp-block-cover .wp-block-cover__gradient-background.has-background-dim.has-background-dim-100, .wp-block-cover-image .wp-block-cover__background.has-background-dim.has-background-dim-100, .wp-block-cover-image .wp-block-cover__gradient-background.has-background-dim.has-background-dim-100 {
  opacity: 1;
}

.wp-block-cover-image.alignleft, .wp-block-cover-image.alignright, .wp-block-cover.alignleft, .wp-block-cover.alignright {
  max-width: 420px;
  width: 100%;
}

.wp-block-cover-image.aligncenter, .wp-block-cover-image.alignleft, .wp-block-cover-image.alignright, .wp-block-cover.aligncenter, .wp-block-cover.alignleft, .wp-block-cover.alignright {
  display: flex;
}

.wp-block-cover .wp-block-cover__inner-container, .wp-block-cover-image .wp-block-cover__inner-container {
  color: inherit;
  position: relative;
  width: 100%;
}

.wp-block-cover-image.is-position-top-left, .wp-block-cover.is-position-top-left {
  align-items: flex-start;
  justify-content: flex-start;
}

.wp-block-cover-image.is-position-top-center, .wp-block-cover.is-position-top-center {
  align-items: flex-start;
  justify-content: center;
}

.wp-block-cover-image.is-position-top-right, .wp-block-cover.is-position-top-right {
  align-items: flex-start;
  justify-content: flex-end;
}

.wp-block-cover-image.is-position-center-left, .wp-block-cover.is-position-center-left {
  align-items: center;
  justify-content: flex-start;
}

.wp-block-cover-image.is-position-center-center, .wp-block-cover.is-position-center-center {
  align-items: center;
  justify-content: center;
}

.wp-block-cover-image.is-position-center-right, .wp-block-cover.is-position-center-right {
  align-items: center;
  justify-content: flex-end;
}

.wp-block-cover-image.is-position-bottom-left, .wp-block-cover.is-position-bottom-left {
  align-items: flex-end;
  justify-content: flex-start;
}

.wp-block-cover-image.is-position-bottom-center, .wp-block-cover.is-position-bottom-center {
  align-items: flex-end;
  justify-content: center;
}

.wp-block-cover-image.is-position-bottom-right, .wp-block-cover.is-position-bottom-right {
  align-items: flex-end;
  justify-content: flex-end;
}

.wp-block-cover-image.has-custom-content-position.has-custom-content-position .wp-block-cover__inner-container, .wp-block-cover.has-custom-content-position.has-custom-content-position .wp-block-cover__inner-container {
  margin: 0;
}

.wp-block-cover-image.has-custom-content-position.has-custom-content-position.is-position-bottom-left .wp-block-cover__inner-container, .wp-block-cover-image.has-custom-content-position.has-custom-content-position.is-position-bottom-right .wp-block-cover__inner-container, .wp-block-cover-image.has-custom-content-position.has-custom-content-position.is-position-center-left .wp-block-cover__inner-container, .wp-block-cover-image.has-custom-content-position.has-custom-content-position.is-position-center-right .wp-block-cover__inner-container, .wp-block-cover-image.has-custom-content-position.has-custom-content-position.is-position-top-left .wp-block-cover__inner-container, .wp-block-cover-image.has-custom-content-position.has-custom-content-position.is-position-top-right .wp-block-cover__inner-container, .wp-block-cover.has-custom-content-position.has-custom-content-position.is-position-bottom-left .wp-block-cover__inner-container, .wp-block-cover.has-custom-content-position.has-custom-content-position.is-position-bottom-right .wp-block-cover__inner-container, .wp-block-cover.has-custom-content-position.has-custom-content-position.is-position-center-left .wp-block-cover__inner-container, .wp-block-cover.has-custom-content-position.has-custom-content-position.is-position-center-right .wp-block-cover__inner-container, .wp-block-cover.has-custom-content-position.has-custom-content-position.is-position-top-left .wp-block-cover__inner-container, .wp-block-cover.has-custom-content-position.has-custom-content-position.is-position-top-right .wp-block-cover__inner-container {
  margin: 0;
  width: auto;
}

.wp-block-cover .wp-block-cover__image-background, .wp-block-cover video.wp-block-cover__video-background, .wp-block-cover-image .wp-block-cover__image-background, .wp-block-cover-image video.wp-block-cover__video-background {
  border: none;
  bottom: 0;
  box-shadow: none;
  height: 100%;
  left: 0;
  margin: 0;
  max-height: none;
  max-width: none;
  object-fit: cover;
  outline: none;
  padding: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
}

.wp-block-cover-image.has-parallax, .wp-block-cover.has-parallax, .wp-block-cover__image-background.has-parallax, video.wp-block-cover__video-background.has-parallax {
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-size: cover;
}

@supports (-webkit-touch-callout: inherit) {
  .wp-block-cover-image.has-parallax, .wp-block-cover.has-parallax, .wp-block-cover__image-background.has-parallax, video.wp-block-cover__video-background.has-parallax {
    background-attachment: scroll;
  }
}
@media (prefers-reduced-motion: reduce) {
  .wp-block-cover-image.has-parallax, .wp-block-cover.has-parallax, .wp-block-cover__image-background.has-parallax, video.wp-block-cover__video-background.has-parallax {
    background-attachment: scroll;
  }
}
.wp-block-cover-image.is-repeated, .wp-block-cover.is-repeated, .wp-block-cover__image-background.is-repeated, video.wp-block-cover__video-background.is-repeated {
  background-repeat: repeat;
  background-size: auto;
}

.wp-block-cover-image-text, .wp-block-cover-image-text a, .wp-block-cover-image-text a:active, .wp-block-cover-image-text a:focus, .wp-block-cover-image-text a:hover, .wp-block-cover-text, .wp-block-cover-text a, .wp-block-cover-text a:active, .wp-block-cover-text a:focus, .wp-block-cover-text a:hover, section.wp-block-cover-image h2, section.wp-block-cover-image h2 a, section.wp-block-cover-image h2 a:active, section.wp-block-cover-image h2 a:focus, section.wp-block-cover-image h2 a:hover {
  color: #fff;
}

.wp-block-cover-image .wp-block-cover.has-left-content {
  justify-content: flex-start;
}

.wp-block-cover-image .wp-block-cover.has-right-content {
  justify-content: flex-end;
}

.wp-block-cover-image.has-left-content .wp-block-cover-image-text, .wp-block-cover.has-left-content .wp-block-cover-text, section.wp-block-cover-image.has-left-content > h2 {
  margin-left: 0;
  text-align: left;
}

.wp-block-cover-image.has-right-content .wp-block-cover-image-text, .wp-block-cover.has-right-content .wp-block-cover-text, section.wp-block-cover-image.has-right-content > h2 {
  margin-right: 0;
  text-align: right;
}

.wp-block-cover .wp-block-cover-text, .wp-block-cover-image .wp-block-cover-image-text, section.wp-block-cover-image > h2 {
  font-size: 2em;
  line-height: 1.25;
  margin-bottom: 0;
  max-width: 840px;
  padding: 0.44em;
  text-align: center;
  z-index: 1;
}

:where(.wp-block-cover-image:not(.has-text-color)), :where(.wp-block-cover:not(.has-text-color)) {
  color: #fff;
}

:where(.wp-block-cover-image.is-light:not(.has-text-color)), :where(.wp-block-cover.is-light:not(.has-text-color)) {
  color: #000;
}

:root :where(.wp-block-cover h1:not(.has-text-color)), :root :where(.wp-block-cover h2:not(.has-text-color)), :root :where(.wp-block-cover h3:not(.has-text-color)), :root :where(.wp-block-cover h4:not(.has-text-color)), :root :where(.wp-block-cover h5:not(.has-text-color)), :root :where(.wp-block-cover h6:not(.has-text-color)), :root :where(.wp-block-cover p:not(.has-text-color)) {
  color: inherit;
}

body:not(.editor-styles-wrapper) .wp-block-cover:not(.wp-block-cover:has(.wp-block-cover__background + .wp-block-cover__inner-container)) .wp-block-cover__image-background, body:not(.editor-styles-wrapper) .wp-block-cover:not(.wp-block-cover:has(.wp-block-cover__background + .wp-block-cover__inner-container)) .wp-block-cover__video-background {
  z-index: 0;
}

body:not(.editor-styles-wrapper) .wp-block-cover:not(.wp-block-cover:has(.wp-block-cover__background + .wp-block-cover__inner-container)) .wp-block-cover__background, body:not(.editor-styles-wrapper) .wp-block-cover:not(.wp-block-cover:has(.wp-block-cover__background + .wp-block-cover__inner-container)) .wp-block-cover__gradient-background, body:not(.editor-styles-wrapper) .wp-block-cover:not(.wp-block-cover:has(.wp-block-cover__background + .wp-block-cover__inner-container)) .wp-block-cover__inner-container, body:not(.editor-styles-wrapper) .wp-block-cover:not(.wp-block-cover:has(.wp-block-cover__background + .wp-block-cover__inner-container)).has-background-dim:not(.has-background-gradient):before {
  z-index: 1;
}

.has-modal-open body:not(.editor-styles-wrapper) .wp-block-cover:not(.wp-block-cover:has(.wp-block-cover__background + .wp-block-cover__inner-container)) .wp-block-cover__inner-container {
  z-index: auto;
}

.wp-block-details {
  box-sizing: border-box;
}

.wp-block-details summary {
  cursor: pointer;
}

.wp-block-embed.alignleft, .wp-block-embed.alignright, .wp-block[data-align=left] > [data-type="core/embed"], .wp-block[data-align=right] > [data-type="core/embed"] {
  max-width: 360px;
  width: 100%;
}

.wp-block-embed.alignleft .wp-block-embed__wrapper, .wp-block-embed.alignright .wp-block-embed__wrapper, .wp-block[data-align=left] > [data-type="core/embed"] .wp-block-embed__wrapper, .wp-block[data-align=right] > [data-type="core/embed"] .wp-block-embed__wrapper {
  min-width: 280px;
}

.wp-block-cover .wp-block-embed {
  min-height: 240px;
  min-width: 320px;
}

.wp-block-embed {
  overflow-wrap: break-word;
}

.wp-block-embed :where(figcaption) {
  margin-bottom: 1em;
  margin-top: 0.5em;
}

.wp-block-embed iframe {
  max-width: 100%;
}

.wp-block-embed__wrapper {
  position: relative;
}

.wp-embed-responsive .wp-has-aspect-ratio .wp-block-embed__wrapper:before {
  content: "";
  display: block;
  padding-top: 50%;
}

.wp-embed-responsive .wp-has-aspect-ratio iframe {
  bottom: 0;
  height: 100%;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
}

.wp-embed-responsive .wp-embed-aspect-21-9 .wp-block-embed__wrapper:before {
  padding-top: 42.85%;
}

.wp-embed-responsive .wp-embed-aspect-18-9 .wp-block-embed__wrapper:before {
  padding-top: 50%;
}

.wp-embed-responsive .wp-embed-aspect-16-9 .wp-block-embed__wrapper:before {
  padding-top: 56.25%;
}

.wp-embed-responsive .wp-embed-aspect-4-3 .wp-block-embed__wrapper:before {
  padding-top: 75%;
}

.wp-embed-responsive .wp-embed-aspect-1-1 .wp-block-embed__wrapper:before {
  padding-top: 100%;
}

.wp-embed-responsive .wp-embed-aspect-9-16 .wp-block-embed__wrapper:before {
  padding-top: 177.77%;
}

.wp-embed-responsive .wp-embed-aspect-1-2 .wp-block-embed__wrapper:before {
  padding-top: 200%;
}

.wp-block-file {
  box-sizing: border-box;
}

.wp-block-file:not(.wp-element-button) {
  font-size: 0.8em;
}

.wp-block-file.aligncenter {
  text-align: center;
}

.wp-block-file.alignright {
  text-align: right;
}

.wp-block-file * + .wp-block-file__button {
  margin-left: 0.75em;
}

:where(.wp-block-file) {
  margin-bottom: 1.5em;
}

.wp-block-file__embed {
  margin-bottom: 1em;
}

:where(.wp-block-file__button) {
  border-radius: 2em;
  display: inline-block;
  padding: 0.5em 1em;
}

:where(.wp-block-file__button):is(a):active, :where(.wp-block-file__button):is(a):focus, :where(.wp-block-file__button):is(a):hover, :where(.wp-block-file__button):is(a):visited {
  box-shadow: none;
  color: #fff;
  opacity: 0.85;
  text-decoration: none;
}

.blocks-gallery-grid:not(.has-nested-images), .wp-block-gallery:not(.has-nested-images) {
  display: flex;
  flex-wrap: wrap;
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image, .blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item, .wp-block-gallery:not(.has-nested-images) .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images) .blocks-gallery-item {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  justify-content: center;
  margin: 0 1em 1em 0;
  position: relative;
  width: calc(50% - 1em);
}

.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image:nth-of-type(2n), .blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item:nth-of-type(2n), .wp-block-gallery:not(.has-nested-images) .blocks-gallery-image:nth-of-type(2n), .wp-block-gallery:not(.has-nested-images) .blocks-gallery-item:nth-of-type(2n) {
  margin-right: 0;
}

.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image figure, .blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figure, .wp-block-gallery:not(.has-nested-images) .blocks-gallery-image figure, .wp-block-gallery:not(.has-nested-images) .blocks-gallery-item figure {
  align-items: flex-end;
  display: flex;
  height: 100%;
  justify-content: flex-start;
  margin: 0;
}

.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image img, .blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item img, .wp-block-gallery:not(.has-nested-images) .blocks-gallery-image img, .wp-block-gallery:not(.has-nested-images) .blocks-gallery-item img {
  display: block;
  height: auto;
  max-width: 100%;
  width: auto;
}

.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image figcaption, .blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figcaption, .wp-block-gallery:not(.has-nested-images) .blocks-gallery-image figcaption, .wp-block-gallery:not(.has-nested-images) .blocks-gallery-item figcaption {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.7019607843), rgba(0, 0, 0, 0.3019607843) 70%, rgba(0, 0, 0, 0));
  bottom: 0;
  box-sizing: border-box;
  color: #fff;
  font-size: 0.8em;
  margin: 0;
  max-height: 100%;
  overflow: auto;
  padding: 3em 0.77em 0.7em;
  position: absolute;
  text-align: center;
  width: 100%;
  z-index: 2;
}

.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image figcaption img, .blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figcaption img, .wp-block-gallery:not(.has-nested-images) .blocks-gallery-image figcaption img, .wp-block-gallery:not(.has-nested-images) .blocks-gallery-item figcaption img {
  display: inline;
}

.blocks-gallery-grid:not(.has-nested-images) figcaption, .wp-block-gallery:not(.has-nested-images) figcaption {
  flex-grow: 1;
}

.blocks-gallery-grid:not(.has-nested-images).is-cropped .blocks-gallery-image a, .blocks-gallery-grid:not(.has-nested-images).is-cropped .blocks-gallery-image img, .blocks-gallery-grid:not(.has-nested-images).is-cropped .blocks-gallery-item a, .blocks-gallery-grid:not(.has-nested-images).is-cropped .blocks-gallery-item img, .wp-block-gallery:not(.has-nested-images).is-cropped .blocks-gallery-image a, .wp-block-gallery:not(.has-nested-images).is-cropped .blocks-gallery-image img, .wp-block-gallery:not(.has-nested-images).is-cropped .blocks-gallery-item a, .wp-block-gallery:not(.has-nested-images).is-cropped .blocks-gallery-item img {
  flex: 1;
  height: 100%;
  object-fit: cover;
  width: 100%;
}

.blocks-gallery-grid:not(.has-nested-images).columns-1 .blocks-gallery-image, .blocks-gallery-grid:not(.has-nested-images).columns-1 .blocks-gallery-item, .wp-block-gallery:not(.has-nested-images).columns-1 .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images).columns-1 .blocks-gallery-item {
  margin-right: 0;
  width: 100%;
}

@media (min-width: 600px) {
  .blocks-gallery-grid:not(.has-nested-images).columns-3 .blocks-gallery-image, .blocks-gallery-grid:not(.has-nested-images).columns-3 .blocks-gallery-item, .wp-block-gallery:not(.has-nested-images).columns-3 .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images).columns-3 .blocks-gallery-item {
    margin-right: 1em;
    width: calc(33.33333% - 0.66667em);
  }
  .blocks-gallery-grid:not(.has-nested-images).columns-4 .blocks-gallery-image, .blocks-gallery-grid:not(.has-nested-images).columns-4 .blocks-gallery-item, .wp-block-gallery:not(.has-nested-images).columns-4 .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images).columns-4 .blocks-gallery-item {
    margin-right: 1em;
    width: calc(25% - 0.75em);
  }
  .blocks-gallery-grid:not(.has-nested-images).columns-5 .blocks-gallery-image, .blocks-gallery-grid:not(.has-nested-images).columns-5 .blocks-gallery-item, .wp-block-gallery:not(.has-nested-images).columns-5 .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images).columns-5 .blocks-gallery-item {
    margin-right: 1em;
    width: calc(20% - 0.8em);
  }
  .blocks-gallery-grid:not(.has-nested-images).columns-6 .blocks-gallery-image, .blocks-gallery-grid:not(.has-nested-images).columns-6 .blocks-gallery-item, .wp-block-gallery:not(.has-nested-images).columns-6 .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images).columns-6 .blocks-gallery-item {
    margin-right: 1em;
    width: calc(16.66667% - 0.83333em);
  }
  .blocks-gallery-grid:not(.has-nested-images).columns-7 .blocks-gallery-image, .blocks-gallery-grid:not(.has-nested-images).columns-7 .blocks-gallery-item, .wp-block-gallery:not(.has-nested-images).columns-7 .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images).columns-7 .blocks-gallery-item {
    margin-right: 1em;
    width: calc(14.28571% - 0.85714em);
  }
  .blocks-gallery-grid:not(.has-nested-images).columns-8 .blocks-gallery-image, .blocks-gallery-grid:not(.has-nested-images).columns-8 .blocks-gallery-item, .wp-block-gallery:not(.has-nested-images).columns-8 .blocks-gallery-image, .wp-block-gallery:not(.has-nested-images).columns-8 .blocks-gallery-item {
    margin-right: 1em;
    width: calc(12.5% - 0.875em);
  }
  .blocks-gallery-grid:not(.has-nested-images).columns-1 .blocks-gallery-image:nth-of-type(1n), .blocks-gallery-grid:not(.has-nested-images).columns-1 .blocks-gallery-item:nth-of-type(1n), .blocks-gallery-grid:not(.has-nested-images).columns-2 .blocks-gallery-image:nth-of-type(2n), .blocks-gallery-grid:not(.has-nested-images).columns-2 .blocks-gallery-item:nth-of-type(2n), .blocks-gallery-grid:not(.has-nested-images).columns-3 .blocks-gallery-image:nth-of-type(3n), .blocks-gallery-grid:not(.has-nested-images).columns-3 .blocks-gallery-item:nth-of-type(3n), .blocks-gallery-grid:not(.has-nested-images).columns-4 .blocks-gallery-image:nth-of-type(4n), .blocks-gallery-grid:not(.has-nested-images).columns-4 .blocks-gallery-item:nth-of-type(4n), .blocks-gallery-grid:not(.has-nested-images).columns-5 .blocks-gallery-image:nth-of-type(5n), .blocks-gallery-grid:not(.has-nested-images).columns-5 .blocks-gallery-item:nth-of-type(5n), .blocks-gallery-grid:not(.has-nested-images).columns-6 .blocks-gallery-image:nth-of-type(6n), .blocks-gallery-grid:not(.has-nested-images).columns-6 .blocks-gallery-item:nth-of-type(6n), .blocks-gallery-grid:not(.has-nested-images).columns-7 .blocks-gallery-image:nth-of-type(7n), .blocks-gallery-grid:not(.has-nested-images).columns-7 .blocks-gallery-item:nth-of-type(7n), .blocks-gallery-grid:not(.has-nested-images).columns-8 .blocks-gallery-image:nth-of-type(8n), .blocks-gallery-grid:not(.has-nested-images).columns-8 .blocks-gallery-item:nth-of-type(8n), .wp-block-gallery:not(.has-nested-images).columns-1 .blocks-gallery-image:nth-of-type(1n), .wp-block-gallery:not(.has-nested-images).columns-1 .blocks-gallery-item:nth-of-type(1n), .wp-block-gallery:not(.has-nested-images).columns-2 .blocks-gallery-image:nth-of-type(2n), .wp-block-gallery:not(.has-nested-images).columns-2 .blocks-gallery-item:nth-of-type(2n), .wp-block-gallery:not(.has-nested-images).columns-3 .blocks-gallery-image:nth-of-type(3n), .wp-block-gallery:not(.has-nested-images).columns-3 .blocks-gallery-item:nth-of-type(3n), .wp-block-gallery:not(.has-nested-images).columns-4 .blocks-gallery-image:nth-of-type(4n), .wp-block-gallery:not(.has-nested-images).columns-4 .blocks-gallery-item:nth-of-type(4n), .wp-block-gallery:not(.has-nested-images).columns-5 .blocks-gallery-image:nth-of-type(5n), .wp-block-gallery:not(.has-nested-images).columns-5 .blocks-gallery-item:nth-of-type(5n), .wp-block-gallery:not(.has-nested-images).columns-6 .blocks-gallery-image:nth-of-type(6n), .wp-block-gallery:not(.has-nested-images).columns-6 .blocks-gallery-item:nth-of-type(6n), .wp-block-gallery:not(.has-nested-images).columns-7 .blocks-gallery-image:nth-of-type(7n), .wp-block-gallery:not(.has-nested-images).columns-7 .blocks-gallery-item:nth-of-type(7n), .wp-block-gallery:not(.has-nested-images).columns-8 .blocks-gallery-image:nth-of-type(8n), .wp-block-gallery:not(.has-nested-images).columns-8 .blocks-gallery-item:nth-of-type(8n) {
    margin-right: 0;
  }
}
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-image:last-child, .blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item:last-child, .wp-block-gallery:not(.has-nested-images) .blocks-gallery-image:last-child, .wp-block-gallery:not(.has-nested-images) .blocks-gallery-item:last-child {
  margin-right: 0;
}

.blocks-gallery-grid:not(.has-nested-images).alignleft, .blocks-gallery-grid:not(.has-nested-images).alignright, .wp-block-gallery:not(.has-nested-images).alignleft, .wp-block-gallery:not(.has-nested-images).alignright {
  max-width: 420px;
  width: 100%;
}

.blocks-gallery-grid:not(.has-nested-images).aligncenter .blocks-gallery-item figure, .wp-block-gallery:not(.has-nested-images).aligncenter .blocks-gallery-item figure {
  justify-content: center;
}

.wp-block-gallery:not(.is-cropped) .blocks-gallery-item {
  align-self: flex-start;
}

figure.wp-block-gallery.has-nested-images {
  align-items: normal;
}

.wp-block-gallery.has-nested-images figure.wp-block-image:not(#individual-image) {
  margin: 0;
  width: calc(50% - var(--wp--style--unstable-gallery-gap, 16px) / 2);
}

.wp-block-gallery.has-nested-images figure.wp-block-image {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  justify-content: center;
  max-width: 100%;
  position: relative;
}

.wp-block-gallery.has-nested-images figure.wp-block-image > a, .wp-block-gallery.has-nested-images figure.wp-block-image > div {
  flex-direction: column;
  flex-grow: 1;
  margin: 0;
}

.wp-block-gallery.has-nested-images figure.wp-block-image img {
  display: block;
  height: auto;
  max-width: 100% !important;
  width: auto;
}

.wp-block-gallery.has-nested-images figure.wp-block-image figcaption, .wp-block-gallery.has-nested-images figure.wp-block-image:has(figcaption):before {
  bottom: 0;
  left: 0;
  max-height: 100%;
  position: absolute;
  right: 0;
}

.wp-block-gallery.has-nested-images figure.wp-block-image:has(figcaption):before {
  -webkit-backdrop-filter: blur(3px);
  backdrop-filter: blur(3px);
  content: "";
  height: 100%;
  -webkit-mask-image: linear-gradient(0deg, #000 20%, rgba(0, 0, 0, 0));
  mask-image: linear-gradient(0deg, #000 20%, rgba(0, 0, 0, 0));
  max-height: 40%;
}

.wp-block-gallery.has-nested-images figure.wp-block-image figcaption {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0));
  box-sizing: border-box;
  color: #fff;
  font-size: 13px;
  margin: 0;
  overflow: auto;
  padding: 1em;
  scrollbar-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0);
  scrollbar-gutter: stable both-edges;
  scrollbar-width: thin;
  text-align: center;
  text-shadow: 0 0 1.5px #000;
  will-change: transform;
}

.wp-block-gallery.has-nested-images figure.wp-block-image figcaption::-webkit-scrollbar {
  height: 12px;
  width: 12px;
}

.wp-block-gallery.has-nested-images figure.wp-block-image figcaption::-webkit-scrollbar-track {
  background-color: initial;
}

.wp-block-gallery.has-nested-images figure.wp-block-image figcaption::-webkit-scrollbar-thumb {
  background-clip: padding-box;
  background-color: initial;
  border: 3px solid rgba(0, 0, 0, 0);
  border-radius: 8px;
}

.wp-block-gallery.has-nested-images figure.wp-block-image figcaption:focus-within::-webkit-scrollbar-thumb, .wp-block-gallery.has-nested-images figure.wp-block-image figcaption:focus::-webkit-scrollbar-thumb, .wp-block-gallery.has-nested-images figure.wp-block-image figcaption:hover::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.8);
}

.wp-block-gallery.has-nested-images figure.wp-block-image figcaption:focus, .wp-block-gallery.has-nested-images figure.wp-block-image figcaption:focus-within, .wp-block-gallery.has-nested-images figure.wp-block-image figcaption:hover {
  scrollbar-color: rgba(255, 255, 255, 0.8) rgba(0, 0, 0, 0);
}

@media (hover: none) {
  .wp-block-gallery.has-nested-images figure.wp-block-image figcaption {
    scrollbar-color: rgba(255, 255, 255, 0.8) rgba(0, 0, 0, 0);
  }
}
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption img {
  display: inline;
}

.wp-block-gallery.has-nested-images figure.wp-block-image figcaption a {
  color: inherit;
}

.wp-block-gallery.has-nested-images figure.wp-block-image.has-custom-border img {
  box-sizing: border-box;
}

.wp-block-gallery.has-nested-images figure.wp-block-image.has-custom-border > a, .wp-block-gallery.has-nested-images figure.wp-block-image.has-custom-border > div, .wp-block-gallery.has-nested-images figure.wp-block-image.is-style-rounded > a, .wp-block-gallery.has-nested-images figure.wp-block-image.is-style-rounded > div {
  flex: 1 1 auto;
}

.wp-block-gallery.has-nested-images figure.wp-block-image.has-custom-border figcaption, .wp-block-gallery.has-nested-images figure.wp-block-image.is-style-rounded figcaption {
  background: none;
  color: inherit;
  flex: initial;
  margin: 0;
  padding: 10px 10px 9px;
  position: relative;
  text-shadow: none;
}

.wp-block-gallery.has-nested-images figure.wp-block-image.has-custom-border:before, .wp-block-gallery.has-nested-images figure.wp-block-image.is-style-rounded:before {
  content: none;
}

.wp-block-gallery.has-nested-images figcaption {
  flex-basis: 100%;
  flex-grow: 1;
  text-align: center;
}

.wp-block-gallery.has-nested-images:not(.is-cropped) figure.wp-block-image:not(#individual-image) {
  margin-bottom: auto;
  margin-top: 0;
}

.wp-block-gallery.has-nested-images.is-cropped figure.wp-block-image:not(#individual-image) {
  align-self: inherit;
}

.wp-block-gallery.has-nested-images.is-cropped figure.wp-block-image:not(#individual-image) > a, .wp-block-gallery.has-nested-images.is-cropped figure.wp-block-image:not(#individual-image) > div:not(.components-drop-zone) {
  display: flex;
}

.wp-block-gallery.has-nested-images.is-cropped figure.wp-block-image:not(#individual-image) a, .wp-block-gallery.has-nested-images.is-cropped figure.wp-block-image:not(#individual-image) img {
  flex: 1 0 0%;
  height: 100%;
  object-fit: cover;
  width: 100%;
}

.wp-block-gallery.has-nested-images.columns-1 figure.wp-block-image:not(#individual-image) {
  width: 100%;
}

@media (min-width: 600px) {
  .wp-block-gallery.has-nested-images.columns-3 figure.wp-block-image:not(#individual-image) {
    width: calc(33.33333% - var(--wp--style--unstable-gallery-gap, 16px) * 0.66667);
  }
  .wp-block-gallery.has-nested-images.columns-4 figure.wp-block-image:not(#individual-image) {
    width: calc(25% - var(--wp--style--unstable-gallery-gap, 16px) * 0.75);
  }
  .wp-block-gallery.has-nested-images.columns-5 figure.wp-block-image:not(#individual-image) {
    width: calc(20% - var(--wp--style--unstable-gallery-gap, 16px) * 0.8);
  }
  .wp-block-gallery.has-nested-images.columns-6 figure.wp-block-image:not(#individual-image) {
    width: calc(16.66667% - var(--wp--style--unstable-gallery-gap, 16px) * 0.83333);
  }
  .wp-block-gallery.has-nested-images.columns-7 figure.wp-block-image:not(#individual-image) {
    width: calc(14.28571% - var(--wp--style--unstable-gallery-gap, 16px) * 0.85714);
  }
  .wp-block-gallery.has-nested-images.columns-8 figure.wp-block-image:not(#individual-image) {
    width: calc(12.5% - var(--wp--style--unstable-gallery-gap, 16px) * 0.875);
  }
  .wp-block-gallery.has-nested-images.columns-default figure.wp-block-image:not(#individual-image) {
    width: calc(33.33% - var(--wp--style--unstable-gallery-gap, 16px) * 0.66667);
  }
  .wp-block-gallery.has-nested-images.columns-default figure.wp-block-image:not(#individual-image):first-child:nth-last-child(2), .wp-block-gallery.has-nested-images.columns-default figure.wp-block-image:not(#individual-image):first-child:nth-last-child(2) ~ figure.wp-block-image:not(#individual-image) {
    width: calc(50% - var(--wp--style--unstable-gallery-gap, 16px) * 0.5);
  }
  .wp-block-gallery.has-nested-images.columns-default figure.wp-block-image:not(#individual-image):first-child:last-child {
    width: 100%;
  }
}
.wp-block-gallery.has-nested-images.alignleft, .wp-block-gallery.has-nested-images.alignright {
  max-width: 420px;
  width: 100%;
}

.wp-block-gallery.has-nested-images.aligncenter {
  justify-content: center;
}

.wp-block-group {
  box-sizing: border-box;
}

:where(.wp-block-group.wp-block-group-is-layout-constrained) {
  position: relative;
}

h1.has-background, h2.has-background, h3.has-background, h4.has-background, h5.has-background, h6.has-background {
  padding: 1.25em 2.375em;
}

h1.has-text-align-left[style*=writing-mode]:where([style*=vertical-lr]), h1.has-text-align-right[style*=writing-mode]:where([style*=vertical-rl]), h2.has-text-align-left[style*=writing-mode]:where([style*=vertical-lr]), h2.has-text-align-right[style*=writing-mode]:where([style*=vertical-rl]), h3.has-text-align-left[style*=writing-mode]:where([style*=vertical-lr]), h3.has-text-align-right[style*=writing-mode]:where([style*=vertical-rl]), h4.has-text-align-left[style*=writing-mode]:where([style*=vertical-lr]), h4.has-text-align-right[style*=writing-mode]:where([style*=vertical-rl]), h5.has-text-align-left[style*=writing-mode]:where([style*=vertical-lr]), h5.has-text-align-right[style*=writing-mode]:where([style*=vertical-rl]), h6.has-text-align-left[style*=writing-mode]:where([style*=vertical-lr]), h6.has-text-align-right[style*=writing-mode]:where([style*=vertical-rl]) {
  rotate: 180deg;
}

.wp-block-image > a, .wp-block-image > figure > a {
  display: inline-block;
}

.wp-block-image img {
  box-sizing: border-box;
  height: auto;
  max-width: 100%;
  vertical-align: bottom;
}

@media not (prefers-reduced-motion) {
  .wp-block-image img.hide {
    visibility: hidden;
  }
  .wp-block-image img.show {
    animation: show-content-image 0.4s;
  }
}
.wp-block-image[style*=border-radius] img, .wp-block-image[style*=border-radius] > a {
  border-radius: inherit;
}

.wp-block-image.has-custom-border img {
  box-sizing: border-box;
}

.wp-block-image.aligncenter {
  text-align: center;
}

.wp-block-image.alignfull > a, .wp-block-image.alignwide > a {
  width: 100%;
}

.wp-block-image.alignfull img, .wp-block-image.alignwide img {
  height: auto;
  width: 100%;
}

.wp-block-image .aligncenter, .wp-block-image .alignleft, .wp-block-image .alignright, .wp-block-image.aligncenter, .wp-block-image.alignleft, .wp-block-image.alignright {
  display: table;
}

.wp-block-image .aligncenter > figcaption, .wp-block-image .alignleft > figcaption, .wp-block-image .alignright > figcaption, .wp-block-image.aligncenter > figcaption, .wp-block-image.alignleft > figcaption, .wp-block-image.alignright > figcaption {
  caption-side: bottom;
  display: table-caption;
}

.wp-block-image .alignleft {
  float: left;
  margin: 0.5em 1em 0.5em 0;
}

.wp-block-image .alignright {
  float: right;
  margin: 0.5em 0 0.5em 1em;
}

.wp-block-image .aligncenter {
  margin-left: auto;
  margin-right: auto;
}

.wp-block-image :where(figcaption) {
  margin-bottom: 1em;
  margin-top: 0.5em;
}

.wp-block-image.is-style-circle-mask img {
  border-radius: 9999px;
}

@supports (-webkit-mask-image: none) or (mask-image: none) or (-webkit-mask-image: none) {
  .wp-block-image.is-style-circle-mask img {
    border-radius: 0;
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="50"/></svg>');
    mask-image: url('data:image/svg+xml;utf8,<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="50"/></svg>');
    mask-mode: alpha;
    -webkit-mask-position: center;
    mask-position: center;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: contain;
    mask-size: contain;
  }
}
:root :where(.wp-block-image.is-style-rounded img, .wp-block-image .is-style-rounded img) {
  border-radius: 9999px;
}

.wp-block-image figure {
  margin: 0;
}

.wp-lightbox-container {
  display: flex;
  flex-direction: column;
  position: relative;
}

.wp-lightbox-container img {
  cursor: zoom-in;
}

.wp-lightbox-container img:hover + button {
  opacity: 1;
}

.wp-lightbox-container button {
  align-items: center;
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(90, 90, 90, 0.2509803922);
  border: none;
  border-radius: 4px;
  cursor: zoom-in;
  display: flex;
  height: 20px;
  justify-content: center;
  opacity: 0;
  padding: 0;
  position: absolute;
  right: 16px;
  text-align: center;
  top: 16px;
  width: 20px;
  z-index: 100;
}

@media not (prefers-reduced-motion) {
  .wp-lightbox-container button {
    transition: opacity 0.2s ease;
  }
}
.wp-lightbox-container button:focus-visible {
  outline: 3px auto rgba(90, 90, 90, 0.2509803922);
  outline: 3px auto -webkit-focus-ring-color;
  outline-offset: 3px;
}

.wp-lightbox-container button:hover {
  cursor: pointer;
  opacity: 1;
}

.wp-lightbox-container button:focus {
  opacity: 1;
}

.wp-lightbox-container button:focus, .wp-lightbox-container button:hover, .wp-lightbox-container button:not(:hover):not(:active):not(.has-background) {
  background-color: rgba(90, 90, 90, 0.2509803922);
  border: none;
}

.wp-lightbox-overlay {
  box-sizing: border-box;
  cursor: zoom-out;
  height: 100vh;
  left: 0;
  overflow: hidden;
  position: fixed;
  top: 0;
  visibility: hidden;
  width: 100%;
  z-index: 100000;
}

.wp-lightbox-overlay .close-button {
  align-items: center;
  cursor: pointer;
  display: flex;
  justify-content: center;
  min-height: 40px;
  min-width: 40px;
  padding: 0;
  position: absolute;
  right: calc(env(safe-area-inset-right) + 16px);
  top: calc(env(safe-area-inset-top) + 16px);
  z-index: 5000000;
}

.wp-lightbox-overlay .close-button:focus, .wp-lightbox-overlay .close-button:hover, .wp-lightbox-overlay .close-button:not(:hover):not(:active):not(.has-background) {
  background: none;
  border: none;
}

.wp-lightbox-overlay .lightbox-image-container {
  height: var(--wp--lightbox-container-height);
  left: 50%;
  overflow: hidden;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  transform-origin: top left;
  width: var(--wp--lightbox-container-width);
  z-index: 9999999999;
}

.wp-lightbox-overlay .wp-block-image {
  align-items: center;
  box-sizing: border-box;
  display: flex;
  height: 100%;
  justify-content: center;
  margin: 0;
  position: relative;
  transform-origin: 0 0;
  width: 100%;
  z-index: 3000000;
}

.wp-lightbox-overlay .wp-block-image img {
  height: var(--wp--lightbox-image-height);
  min-height: var(--wp--lightbox-image-height);
  min-width: var(--wp--lightbox-image-width);
  width: var(--wp--lightbox-image-width);
}

.wp-lightbox-overlay .wp-block-image figcaption {
  display: none;
}

.wp-lightbox-overlay button {
  background: none;
  border: none;
}

.wp-lightbox-overlay .scrim {
  background-color: #fff;
  height: 100%;
  opacity: 0.9;
  position: absolute;
  width: 100%;
  z-index: 2000000;
}

.wp-lightbox-overlay.active {
  visibility: visible;
}

@media not (prefers-reduced-motion) {
  .wp-lightbox-overlay.active {
    animation: turn-on-visibility 0.25s both;
  }
  .wp-lightbox-overlay.active img {
    animation: turn-on-visibility 0.35s both;
  }
  .wp-lightbox-overlay.show-closing-animation:not(.active) {
    animation: turn-off-visibility 0.35s both;
  }
  .wp-lightbox-overlay.show-closing-animation:not(.active) img {
    animation: turn-off-visibility 0.25s both;
  }
  .wp-lightbox-overlay.zoom.active {
    animation: none;
    opacity: 1;
    visibility: visible;
  }
  .wp-lightbox-overlay.zoom.active .lightbox-image-container {
    animation: lightbox-zoom-in 0.4s;
  }
  .wp-lightbox-overlay.zoom.active .lightbox-image-container img {
    animation: none;
  }
  .wp-lightbox-overlay.zoom.active .scrim {
    animation: turn-on-visibility 0.4s forwards;
  }
  .wp-lightbox-overlay.zoom.show-closing-animation:not(.active) {
    animation: none;
  }
  .wp-lightbox-overlay.zoom.show-closing-animation:not(.active) .lightbox-image-container {
    animation: lightbox-zoom-out 0.4s;
  }
  .wp-lightbox-overlay.zoom.show-closing-animation:not(.active) .lightbox-image-container img {
    animation: none;
  }
  .wp-lightbox-overlay.zoom.show-closing-animation:not(.active) .scrim {
    animation: turn-off-visibility 0.4s forwards;
  }
}
@keyframes show-content-image {
  0% {
    visibility: hidden;
  }
  99% {
    visibility: hidden;
  }
  to {
    visibility: visible;
  }
}
@keyframes turn-on-visibility {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes turn-off-visibility {
  0% {
    opacity: 1;
    visibility: visible;
  }
  99% {
    opacity: 0;
    visibility: visible;
  }
  to {
    opacity: 0;
    visibility: hidden;
  }
}
@keyframes lightbox-zoom-in {
  0% {
    transform: translate(calc((-100vw + var(--wp--lightbox-scrollbar-width)) / 2 + var(--wp--lightbox-initial-left-position)), calc(-50vh + var(--wp--lightbox-initial-top-position))) scale(var(--wp--lightbox-scale));
  }
  to {
    transform: translate(-50%, -50%) scale(1);
  }
}
@keyframes lightbox-zoom-out {
  0% {
    transform: translate(-50%, -50%) scale(1);
    visibility: visible;
  }
  99% {
    visibility: visible;
  }
  to {
    transform: translate(calc((-100vw + var(--wp--lightbox-scrollbar-width)) / 2 + var(--wp--lightbox-initial-left-position)), calc(-50vh + var(--wp--lightbox-initial-top-position))) scale(var(--wp--lightbox-scale));
    visibility: hidden;
  }
}
ol, ul {
  box-sizing: border-box;
}

:root :where(.wp-block-list.has-background) {
  padding: 1.25em 2.375em;
}

.wp-block-media-text {
  box-sizing: border-box;
  direction: ltr;
  display: grid;
  grid-template-columns: 50% 1fr;
  grid-template-rows: auto;
}

.wp-block-media-text.has-media-on-the-right {
  grid-template-columns: 1fr 50%;
}

.wp-block-media-text.is-vertically-aligned-top > .wp-block-media-text__content, .wp-block-media-text.is-vertically-aligned-top > .wp-block-media-text__media {
  align-self: start;
}

.wp-block-media-text.is-vertically-aligned-center > .wp-block-media-text__content, .wp-block-media-text.is-vertically-aligned-center > .wp-block-media-text__media, .wp-block-media-text > .wp-block-media-text__content, .wp-block-media-text > .wp-block-media-text__media {
  align-self: center;
}

.wp-block-media-text.is-vertically-aligned-bottom > .wp-block-media-text__content, .wp-block-media-text.is-vertically-aligned-bottom > .wp-block-media-text__media {
  align-self: end;
}

.wp-block-media-text > .wp-block-media-text__media {
  grid-column: 1;
  grid-row: 1;
  margin: 0;
}

.wp-block-media-text > .wp-block-media-text__content {
  direction: ltr;
  grid-column: 2;
  grid-row: 1;
  padding: 0 8%;
  word-break: break-word;
}

.wp-block-media-text.has-media-on-the-right > .wp-block-media-text__media {
  grid-column: 2;
  grid-row: 1;
}

.wp-block-media-text.has-media-on-the-right > .wp-block-media-text__content {
  grid-column: 1;
  grid-row: 1;
}

.wp-block-media-text__media a {
  display: block;
}

.wp-block-media-text__media img, .wp-block-media-text__media video {
  height: auto;
  max-width: unset;
  vertical-align: middle;
  width: 100%;
}

.wp-block-media-text.is-image-fill > .wp-block-media-text__media {
  background-size: cover;
  height: 100%;
  min-height: 250px;
}

.wp-block-media-text.is-image-fill > .wp-block-media-text__media > a {
  display: block;
  height: 100%;
}

.wp-block-media-text.is-image-fill > .wp-block-media-text__media img {
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.wp-block-media-text.is-image-fill-element > .wp-block-media-text__media {
  height: 100%;
  min-height: 250px;
  position: relative;
}

.wp-block-media-text.is-image-fill-element > .wp-block-media-text__media > a {
  display: block;
  height: 100%;
}

.wp-block-media-text.is-image-fill-element > .wp-block-media-text__media img {
  height: 100%;
  object-fit: cover;
  position: absolute;
  width: 100%;
}

@media (max-width: 600px) {
  .wp-block-media-text.is-stacked-on-mobile {
    grid-template-columns: 100% !important;
  }
  .wp-block-media-text.is-stacked-on-mobile > .wp-block-media-text__media {
    grid-column: 1;
    grid-row: 1;
  }
  .wp-block-media-text.is-stacked-on-mobile > .wp-block-media-text__content {
    grid-column: 1;
    grid-row: 2;
  }
}
.wp-block-social-links {
  background: none;
  box-sizing: border-box;
  margin-left: 0;
  padding-left: 0;
  padding-right: 0;
  text-indent: 0;
}

.wp-block-social-links .wp-social-link a, .wp-block-social-links .wp-social-link a:hover {
  border-bottom: 0;
  box-shadow: none;
  text-decoration: none;
}

.wp-block-social-links .wp-social-link svg {
  height: 1em;
  width: 1em;
}

.wp-block-social-links .wp-social-link span:not(.screen-reader-text) {
  font-size: 0.65em;
  margin-left: 0.5em;
  margin-right: 0.5em;
}

.wp-block-social-links.has-small-icon-size {
  font-size: 16px;
}

.wp-block-social-links, .wp-block-social-links.has-normal-icon-size {
  font-size: 24px;
}

.wp-block-social-links.has-large-icon-size {
  font-size: 36px;
}

.wp-block-social-links.has-huge-icon-size {
  font-size: 48px;
}

.wp-block-social-links.aligncenter {
  display: flex;
  justify-content: center;
}

.wp-block-social-links.alignright {
  justify-content: flex-end;
}

.wp-block-social-link {
  border-radius: 9999px;
  display: block;
  height: auto;
}

@media not (prefers-reduced-motion) {
  .wp-block-social-link {
    transition: transform 0.1s ease;
  }
}
.wp-block-social-link a {
  align-items: center;
  display: flex;
  line-height: 0;
}

.wp-block-social-link:hover {
  transform: scale(1.1);
}

.wp-block-social-links .wp-block-social-link.wp-social-link {
  display: inline-block;
  margin: 0;
  padding: 0;
}

.wp-block-social-links .wp-block-social-link.wp-social-link .wp-block-social-link-anchor, .wp-block-social-links .wp-block-social-link.wp-social-link .wp-block-social-link-anchor svg, .wp-block-social-links .wp-block-social-link.wp-social-link .wp-block-social-link-anchor:active, .wp-block-social-links .wp-block-social-link.wp-social-link .wp-block-social-link-anchor:hover, .wp-block-social-links .wp-block-social-link.wp-social-link .wp-block-social-link-anchor:visited {
  color: currentColor;
  fill: currentColor;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link {
  background-color: #f0f0f0;
  color: #444;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-amazon {
  background-color: #f90;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-bandcamp {
  background-color: #1ea0c3;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-behance {
  background-color: #0757fe;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-bluesky {
  background-color: #0a7aff;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-codepen {
  background-color: #1e1f26;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-deviantart {
  background-color: #02e49b;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-discord {
  background-color: #5865f2;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-dribbble {
  background-color: #e94c89;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-dropbox {
  background-color: #4280ff;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-etsy {
  background-color: #f45800;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-facebook {
  background-color: #0866ff;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-fivehundredpx {
  background-color: #000;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-flickr {
  background-color: #0461dd;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-foursquare {
  background-color: #e65678;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-github {
  background-color: #24292d;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-goodreads {
  background-color: #eceadd;
  color: #382110;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-google {
  background-color: #ea4434;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-gravatar {
  background-color: #1d4fc4;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-instagram {
  background-color: #f00075;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-lastfm {
  background-color: #e21b24;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-linkedin {
  background-color: #0d66c2;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-mastodon {
  background-color: #3288d4;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-medium {
  background-color: #000;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-meetup {
  background-color: #f6405f;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-patreon {
  background-color: #000;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-pinterest {
  background-color: #e60122;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-pocket {
  background-color: #ef4155;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-reddit {
  background-color: #ff4500;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-skype {
  background-color: #0478d7;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-snapchat {
  background-color: #fefc00;
  color: #fff;
  stroke: #000;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-soundcloud {
  background-color: #ff5600;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-spotify {
  background-color: #1bd760;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-telegram {
  background-color: #2aabee;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-threads {
  background-color: #000;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-tiktok {
  background-color: #000;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-tumblr {
  background-color: #011835;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-twitch {
  background-color: #6440a4;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-twitter {
  background-color: #1da1f2;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-vimeo {
  background-color: #1eb7ea;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-vk {
  background-color: #4680c2;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-wordpress {
  background-color: #3499cd;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-whatsapp {
  background-color: #25d366;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-x {
  background-color: #000;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-yelp {
  background-color: #d32422;
  color: #fff;
}

:where(.wp-block-social-links:not(.is-style-logos-only)) .wp-social-link-youtube {
  background-color: red;
  color: #fff;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link {
  background: none;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link svg {
  height: 1.25em;
  width: 1.25em;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-amazon {
  color: #f90;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-bandcamp {
  color: #1ea0c3;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-behance {
  color: #0757fe;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-bluesky {
  color: #0a7aff;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-codepen {
  color: #1e1f26;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-deviantart {
  color: #02e49b;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-discord {
  color: #5865f2;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-dribbble {
  color: #e94c89;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-dropbox {
  color: #4280ff;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-etsy {
  color: #f45800;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-facebook {
  color: #0866ff;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-fivehundredpx {
  color: #000;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-flickr {
  color: #0461dd;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-foursquare {
  color: #e65678;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-github {
  color: #24292d;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-goodreads {
  color: #382110;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-google {
  color: #ea4434;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-gravatar {
  color: #1d4fc4;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-instagram {
  color: #f00075;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-lastfm {
  color: #e21b24;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-linkedin {
  color: #0d66c2;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-mastodon {
  color: #3288d4;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-medium {
  color: #000;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-meetup {
  color: #f6405f;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-patreon {
  color: #000;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-pinterest {
  color: #e60122;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-pocket {
  color: #ef4155;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-reddit {
  color: #ff4500;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-skype {
  color: #0478d7;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-snapchat {
  color: #fff;
  stroke: #000;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-soundcloud {
  color: #ff5600;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-spotify {
  color: #1bd760;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-telegram {
  color: #2aabee;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-threads {
  color: #000;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-tiktok {
  color: #000;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-tumblr {
  color: #011835;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-twitch {
  color: #6440a4;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-twitter {
  color: #1da1f2;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-vimeo {
  color: #1eb7ea;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-vk {
  color: #4680c2;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-whatsapp {
  color: #25d366;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-wordpress {
  color: #3499cd;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-x {
  color: #000;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-yelp {
  color: #d32422;
}

:where(.wp-block-social-links.is-style-logos-only) .wp-social-link-youtube {
  color: red;
}

.wp-block-social-links.is-style-pill-shape .wp-social-link {
  width: auto;
}

:root :where(.wp-block-social-links .wp-social-link a) {
  padding: 0.25em;
}

:root :where(.wp-block-social-links.is-style-logos-only .wp-social-link a) {
  padding: 0;
}

:root :where(.wp-block-social-links.is-style-pill-shape .wp-social-link a) {
  padding-left: 0.6666666667em;
  padding-right: 0.6666666667em;
}

.wp-block-social-links:not(.has-icon-color):not(.has-icon-background-color) .wp-social-link-snapchat .wp-block-social-link-label {
  color: #000;
}

.wp-block-text-columns, .wp-block-text-columns.aligncenter {
  display: flex;
}

.wp-block-text-columns .wp-block-column {
  margin: 0 1em;
  padding: 0;
}

.wp-block-text-columns .wp-block-column:first-child {
  margin-left: 0;
}

.wp-block-text-columns .wp-block-column:last-child {
  margin-right: 0;
}

.wp-block-text-columns.columns-2 .wp-block-column {
  width: 50%;
}

.wp-block-text-columns.columns-3 .wp-block-column {
  width: 33.3333333333%;
}

.wp-block-text-columns.columns-4 .wp-block-column {
  width: 25%;
}

pre.wp-block-verse {
  overflow: auto;
  white-space: pre-wrap;
}

:where(pre.wp-block-verse) {
  font-family: inherit;
}

.wp-block-video {
  box-sizing: border-box;
}

.wp-block-video video {
  vertical-align: middle;
  width: 100%;
}

@supports (position: sticky) {
  .wp-block-video [poster] {
    object-fit: cover;
  }
}
.wp-block-video.aligncenter {
  text-align: center;
}

.wp-block-video :where(figcaption) {
  margin-bottom: 1em;
  margin-top: 0.5em;
}