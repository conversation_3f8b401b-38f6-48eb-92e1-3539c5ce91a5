@charset "UTF-8";
/* latin-ext */
@font-face {
  font-family: "Montserrat";
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url(../fonts/JTUQjIg1_i6t8kCHKm459WxRxy7m0dR9pBOi.woff2) format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Montserrat";
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url(../fonts/JTUQjIg1_i6t8kCHKm459WxRyS7m0dR9pA.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "Montserrat";
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(../fonts/JTUSjIg1_i6t8kCHKm459WdhyyTh89ZNpQ.woff2) format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Montserrat";
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(../fonts/JTUSjIg1_i6t8kCHKm459WlhyyTh89Y.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "Noticia Text";
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url(../fonts/VuJodNDF2Yv9qppOePKYRP12YwtUn07_pjjsQdA.woff2) format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Noticia Text";
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url(../fonts/VuJodNDF2Yv9qppOePKYRP12Ywtan07_pjjs.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "Noticia Text";
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url(../fonts/VuJrdNDF2Yv9qppOePKYRP12YwPhulvchDXGe9nyfeU.woff2) format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Noticia Text";
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url(../fonts/VuJrdNDF2Yv9qppOePKYRP12YwPhulvShDXGe9ny.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "Noticia Text";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(../fonts/VuJ2dNDF2Yv9qppOePKYRP12aDtYlUndpAjt.woff2) format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Noticia Text";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(../fonts/VuJ2dNDF2Yv9qppOePKYRP12ZjtYlUndpA.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "Noticia Text";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(../fonts/VuJpdNDF2Yv9qppOePKYRP1-3R5NtmvQjjLkeenz.woff2) format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Noticia Text";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(../fonts/VuJpdNDF2Yv9qppOePKYRP1-3R5NuGvQjjLkeQ.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
.container {
  margin-left: auto;
  margin-right: auto;
  width: calc(100% - var(--side-gutter) * 2);
}

@media (max-width: 600px) {
  .hidden-accessible-mob {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
    word-wrap: normal !important;
  }
}
.custom-scrollbar {
  --scrollbarBG: #fff;
  --thumbBG: colours("primary");
  padding-right: 2px;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar {
  --scrollbarBG: #fff;
  --thumbBG: colours("primary");
}
.custom-scrollbar::-webkit-scrollbar {
  -webkit-appearance: none;
  background-color: var(--scrollbarBG);
  height: 4px;
  width: 4px;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: var(--thumbBG);
}

:root {
  --side-gutter: 1.125rem;
}
@media (min-width: 1024px) {
  :root {
    --side-gutter: min(120px, 5vw);
  }
}

html {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
}
html p.wp-block-paragraph {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
  margin-left: auto;
  margin-right: auto;
  max-width: 75rem;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
}

.wp-block-cover-image .block-editor-block-list__block,
.wp-block-cover .block-editor-block-list__block {
  color: inherit;
}

.wp-block,
.editor-rich-text,
.editor-writing-flow,
.mceContentBody {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 20px;
  line-height: 30px;
  text-rendering: antialiased;
}

.wp-block {
  max-width: 75rem;
}

:is(#primary, .is-root-container) :is(.wpdtSimpleTable, .table, .wp-block-table > table) {
  border: 1px solid rgb(228.2, 208, 219.6);
  background: rgb(241.6, 231.5, 237.3);
  border-collapse: collapse;
}
:is(#primary, .is-root-container) :is(.wpdtSimpleTable, .table, .wp-block-table > table) :is(tbody tr td, thead tr td, tbody tr th, thead tr th) {
  border-color: rgb(228.2, 208, 219.6);
}
:is(#primary, .is-root-container) :is(.wpdtSimpleTable, .table, .wp-block-table > table) :is(td, th):is([class*=wpdt-bc-]) {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
  font-weight: 700;
}
:is(#primary, .is-root-container) .wpdt-c.wpDataTableContainerSimpleTable.wdtscroll table.wpdtSimpleTable {
  min-width: 100%;
}

.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container {
  --gap: 40px;
  --wp--style--gallery-gap-default: 40px;
  --gallery-block--gutter-size: 40px;
  --wp--style--block-gap: 40px;
  background: rgb(246.3, 250.6, 249.6);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-heading-1-xl-font-size {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 72px;
  line-height: 80px;
  letter-spacing: -0.01em;
}
@media (max-width: 600px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-heading-1-xl-font-size {
    font-weight: 500;
    font-size: 40px;
    letter-spacing: 0;
    line-height: 44px;
  }
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container h1,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-heading-1-font-size {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 56px;
  line-height: 66px;
  letter-spacing: -0.01em;
}
@media (max-width: 600px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container h1,
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-heading-1-font-size {
    font-size: 36px;
    letter-spacing: 0;
    line-height: 42px;
  }
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container h2,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-heading-2-font-size {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 42px;
  line-height: 52px;
  letter-spacing: -0.01em;
}
@media (max-width: 600px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container h2,
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-heading-2-font-size {
    font-weight: 700;
    font-size: 28px;
    line-height: 34px;
  }
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container h3,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-heading-3-font-size {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 32px;
  letter-spacing: 0;
  line-height: 40px;
}
@media (max-width: 600px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container h3,
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-heading-3-font-size {
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    letter-spacing: 0;
    line-height: 30px;
  }
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container h4,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-heading-4-font-size {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 24px;
  letter-spacing: 0;
  line-height: 28px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container h5,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-heading-5-font-size {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  letter-spacing: 0;
  line-height: 25px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container h6,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-heading-6-font-size {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-body-copy-font-size {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-small-font-size {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(img) {
  height: auto;
  max-width: 100%;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(a) {
  color: currentColor;
  text-decoration: underline;
  text-underline-offset: 3px;
  text-decoration-thickness: 1px;
  transition: all 0.1s;
  word-break: break-word;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(a):hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(a):focus {
  text-decoration-thickness: 2px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container b,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container strong {
  font-weight: 500;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(ol, ul) {
  padding-left: 0.75rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(ol, ul).has-small-font-size {
  padding-left: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(ol, ul) > li {
  margin-top: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(ol, ul) > li:where(:not(:last-child)) {
  margin-bottom: 0.75rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(ol, ul) > li > :is(ol, ul) {
  margin: 0.5rem 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .fine-print {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
  background: rgb(164.2, 186.2, 195.4);
  border-radius: 8px;
  clear: both;
  padding: 0.5rem 0.75rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container h1:is(h1) + :is(.page-subtitle) {
  color: #193b39;
  margin-top: -1.125rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .count-up {
  font-variant-numeric: lining-nums;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container a.anchorlink {
  font-size: 14px;
  position: absolute;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover-image:not(.has-text-color)),
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover:not(.has-text-color)) {
  color: #1a1a1a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover .wp-block-heading:where(:not(.has-text-color)) a {
  color: currentColor;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :root {
  --gap: 40px;
  --wp--style--gallery-gap-default: 40px;
  --gallery-block--gutter-size: 40px;
  --wp--style--block-gap: 40px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.is-layout-grid) {
  gap: var(--gap);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.is-layout-flex) {
  gap: 40px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover .wp-block-cover__inner-container,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover-image .wp-block-cover__inner-container {
  color: unset;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-image {
  margin-bottom: 0;
  margin-top: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(p, ol, ul) {
  margin-bottom: 1.5rem;
  margin-top: 1.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(h1, h2, h3, h4, h5, h6, .wp-block-buttons, .wp-block-columns, .wp-block-cover.aligncenter, .wp-block-group, .wp-block-quote, .wp-block-pullquote, .cards-container, iframe, .wp-block-group-is-layout-grid) {
  margin-bottom: 2.5rem;
  margin-top: 2.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(p, h1, h2, h3, h4, h5, h6, ul, ol, .wp-block-buttons, .wp-block-columns, .wp-block-group, .wp-block-quote, .wp-block-pullquote, .cards-container, iframe, .wp-block-group-is-layout-grid).has-small-font-size {
  margin-bottom: 0.75rem;
  margin-top: 0.75rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(p, h1, h2, h3, h4, h5, h6, ul, ol, .wp-block-buttons, .wp-block-columns, .wp-block-group, .wp-block-quote, .wp-block-pullquote, .cards-container, iframe, .wp-block-group-is-layout-grid):first-child {
  margin-top: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(p, h1, h2, h3, h4, h5, h6, ul, ol, .wp-block-buttons, .wp-block-columns, .wp-block-group, .wp-block-quote, .wp-block-pullquote, .cards-container, iframe, .wp-block-group-is-layout-grid):last-child {
  margin-bottom: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(p, h1, h2, h3, h4, h5, h6, ul, ol, .wp-block-buttons) img {
  display: inline-block;
  margin-right: 0.75rem;
  vertical-align: middle;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container p:has(+ :where(ol, ul)) {
  margin-bottom: 0.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container p + :where(ol, ul) {
  margin-top: 0.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-audio audio {
  display: block;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.cards, .listing, .wp-block-video) {
  margin-bottom: 3rem;
  margin-top: 3rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(p, .wp-block-quote, .has-media-on-the-right .wp-block-media-text__media, .has-media-on-the-left .wp-block-media-text__media, .featured-view, ul, ol) + :where(.wp-block-cover, h1, h2, h3, h4, h5, h6, .has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size) {
  margin-top: 2.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(h1, h2, h3, h4, h5, h6, .has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size) + :is(.cards-container, .listing, .cards) {
  margin-top: 1.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(h1, h2, h3, h4, h5, h6, .has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size) {
  margin-bottom: 0.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(h1, h2, h3, h4, h5, h6, .has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size) + :where(p:not(.has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size), ol, ul) {
  margin-top: 0.75rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(h1, h2, h3, h4, h5, h6, .has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size) + :where(h1, h2, h3, h4, h5, h6, .has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size, .cards-container, .listing, .cards) {
  margin-top: 0.75rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover + :is(p, h1, h2, h3, h4, h5, h6, .has-heading-1-font-size, .has-heading-2-font-size, .has-heading-3-font-size, .has-heading-4-font-size, .has-heading-5-font-size, .has-heading-6-font-size, .listing, .cards-container) {
  margin-top: 2.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-media-text.wp-block-media-text > .wp-block-media-text__content {
  max-width: 560px;
  margin: 0 1.5rem auto auto;
  padding: 3rem 1.125rem;
  width: 100%;
}
@media (min-width: 768px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-media-text.wp-block-media-text.alignfull > .wp-block-media-text__content {
    margin: 0 5rem auto auto;
  }
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-media-text.wp-block-media-text {
  min-height: 400px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-media-text.wp-block-media-text.alignfull {
  min-height: 615px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.entry-content, .site-main) > :is(:not(.wp-block-cover:not(.aligncenter))):last-child {
  margin-bottom: 3rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-underlined {
  border-bottom: solid 1px currentColor;
  margin-bottom: 1.5rem;
  padding-bottom: 4px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-underlined a {
  text-decoration: none;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-body-large-font-size {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 36px;
  letter-spacing: -0.01em;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-body-xl-font-size {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 36px;
  letter-spacing: -0.01em;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-small-copy-font-size {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-x-small-font-size {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-text-align-center {
  text-align: center;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-text-align-right {
  text-align: right;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-quote,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-quote.is-style-large,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-quote.has-text-align-right {
  font-style: normal;
  font-weight: 300;
  font-size: 28px;
  line-height: 36px;
  border: 0;
  letter-spacing: 0.02em;
  margin: 2.5rem auto;
  padding: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-quote p:last-of-type,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-quote.is-style-large p:last-of-type,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-quote.has-text-align-right p:last-of-type {
  margin-bottom: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-quote cite,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-quote.is-style-large cite,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-quote.has-text-align-right cite {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
  display: block;
  margin: 12px 0 0;
  text-transform: none;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-quote cite::before,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-quote.is-style-large cite::before,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-quote.has-text-align-right cite::before {
  content: "— ";
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-pullquote,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-pullquote p {
  font-style: normal;
  font-weight: 300;
  font-size: 28px;
  line-height: 36px;
  text-align: left;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-pullquote .wp-block-pullquote__citation,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-pullquote cite,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-pullquote p .wp-block-pullquote__citation,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-pullquote p cite {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 24px;
  letter-spacing: 0;
  line-height: 28px;
  margin: 15px 0 0;
  text-transform: none;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-buttons {
  display: flex;
  gap: 1.125rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-buttons .wp-block-button {
  margin: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-buttons.is-vertical .wp-block-button {
  margin: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-button__link,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-file .wp-block-file__button {
  align-items: flex-start;
  background: var(--background, rgb(73.4, 117.4, 135.8));
  border-radius: 0;
  border: 0;
  color: #fff;
  cursor: pointer;
  display: inline-flex;
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 18px;
  padding: 0.75rem 2rem;
  text-align: center;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-button__link:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-button__link:hover:visited, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-button__link:focus,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-file .wp-block-file__button:hover,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-file .wp-block-file__button:hover:visited,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-file .wp-block-file__button:focus {
  background: var(--background-hover, #1c536a);
  outline: none;
  text-decoration: underline;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-button__link:focus-visible,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-file .wp-block-file__button:focus-visible {
  box-shadow: inset 0px 0px 0 2px #000;
  outline: 4px solid #f6be46 !important;
  outline-offset: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-small-button .wp-block-button__link {
  padding: 7px 12px 5px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-download .wp-block-button__link {
  align-items: center;
  display: flex;
  gap: 12px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-download .wp-block-button__link:before {
  background: currentColor;
  mask-image: url(../images/download.svg);
  mask-repeat: no-repeat;
  content: "";
  display: block;
  flex: 0 0 19px;
  height: 21px;
  position: relative;
  transition: transform 0.1s ease-in;
  width: 19px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-arrow .wp-block-button__link {
  align-items: center;
  display: flex;
  gap: 12px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-arrow .wp-block-button__link:after {
  background: currentColor;
  mask-image: url(../images/arrow-right.svg);
  mask-repeat: no-repeat;
  content: "";
  display: block;
  flex-shrink: 0;
  height: 12px;
  position: relative;
  transition: transform 0.1s ease-in;
  width: 12px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-arrow .wp-block-button__link:hover:after, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-arrow .wp-block-button__link:focus:after {
  transform: translateX(4px);
  transition: transform 0.2s ease-out;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-arrow-down .wp-block-button__link:after,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-file .wp-block-file__button:after {
  mask-image: url(../images/download.svg);
  mask-repeat: no-repeat;
  flex: 0 0 18px;
  height: 18px;
  width: 18px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-file a.wp-block-file__button:hover,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-file a.wp-block-file__button:focus {
  color: #1a1a1a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-primary-background-color {
  background: var(--background, #1c536a);
  border-color: var(--background, #1c536a);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-primary-color {
  color: #1c536a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-primary-color > a {
  color: currentColor;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-primary-color > a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-primary-color > a:focus-visible {
  text-decoration-color: rgba(28, 83, 106, 0.6);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-primary-color.wp-block-button__link, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-primary-color.wp-block-button__link:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-primary-color.wp-block-button__link:visited:not(:hover) {
  color: #1c536a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-secondary-background-color {
  background: var(--background, #a8d3c9);
  border-color: var(--background, #a8d3c9);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-secondary-color {
  color: #a8d3c9;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-secondary-color > a {
  color: currentColor;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-secondary-color > a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-secondary-color > a:focus-visible {
  text-decoration-color: rgba(168, 211, 201, 0.6);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-secondary-color.wp-block-button__link, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-secondary-color.wp-block-button__link:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-secondary-color.wp-block-button__link:visited:not(:hover) {
  color: #a8d3c9;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-tertiary-background-color {
  background: var(--background, #79144e);
  border-color: var(--background, #79144e);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-tertiary-color {
  color: #79144e;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-tertiary-color > a {
  color: currentColor;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-tertiary-color > a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-tertiary-color > a:focus-visible {
  text-decoration-color: rgba(121, 20, 78, 0.6);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-tertiary-color.wp-block-button__link, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-tertiary-color.wp-block-button__link:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-tertiary-color.wp-block-button__link:visited:not(:hover) {
  color: #79144e;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-white-background-color {
  background: var(--background, #fff);
  border-color: var(--background, #fff);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-white-color {
  color: #fff;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-white-color > a {
  color: currentColor;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-white-color > a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-white-color > a:focus-visible {
  text-decoration-color: rgba(255, 255, 255, 0.6);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-white-color.wp-block-button__link, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-white-color.wp-block-button__link:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-white-color.wp-block-button__link:visited:not(:hover) {
  color: #fff;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-black-background-color {
  background: var(--background, #000);
  border-color: var(--background, #000);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-black-color {
  color: #000;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-black-color > a {
  color: currentColor;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-black-color > a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-black-color > a:focus-visible {
  text-decoration-color: rgba(0, 0, 0, 0.6);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-black-color.wp-block-button__link, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-black-color.wp-block-button__link:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-black-color.wp-block-button__link:visited:not(:hover) {
  color: #000;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-light-background-color {
  background: var(--background, #d7d7d7);
  border-color: var(--background, #d7d7d7);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-light-color {
  color: #d7d7d7;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-light-color > a {
  color: currentColor;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-light-color > a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-light-color > a:focus-visible {
  text-decoration-color: rgba(215, 215, 215, 0.6);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-light-color.wp-block-button__link, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-light-color.wp-block-button__link:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-light-color.wp-block-button__link:visited:not(:hover) {
  color: #d7d7d7;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-dark-background-color {
  background: var(--background, #193b39);
  border-color: var(--background, #193b39);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-dark-color {
  color: #193b39;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-dark-color > a {
  color: currentColor;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-dark-color > a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-dark-color > a:focus-visible {
  text-decoration-color: rgba(25, 59, 57, 0.6);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-dark-color.wp-block-button__link, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-dark-color.wp-block-button__link:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-dark-color.wp-block-button__link:visited:not(:hover) {
  color: #193b39;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-base-background-color {
  background: var(--background, #1a1a1a);
  border-color: var(--background, #1a1a1a);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-base-color {
  color: #1a1a1a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-base-color > a {
  color: currentColor;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-base-color > a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-base-color > a:focus-visible {
  text-decoration-color: rgba(26, 26, 26, 0.6);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-base-color.wp-block-button__link, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-base-color.wp-block-button__link:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-base-color.wp-block-button__link:visited:not(:hover) {
  color: #1a1a1a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-highlight-background-color {
  background: var(--background, #f6be46);
  border-color: var(--background, #f6be46);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-highlight-color {
  color: #f6be46;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-highlight-color > a {
  color: currentColor;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-highlight-color > a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-highlight-color > a:focus-visible {
  text-decoration-color: rgba(246, 190, 70, 0.6);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-highlight-color.wp-block-button__link, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-highlight-color.wp-block-button__link:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-highlight-color.wp-block-button__link:visited:not(:hover) {
  color: #f6be46;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-transparent-background-color {
  background: var(--background, rgba(238, 238, 238, 0));
  border-color: var(--background, rgba(238, 238, 238, 0));
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-transparent-color {
  color: rgba(238, 238, 238, 0);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-transparent-color > a {
  color: currentColor;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-transparent-color > a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-transparent-color > a:focus-visible {
  text-decoration-color: rgba(238, 238, 238, 0.6);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-transparent-color.wp-block-button__link, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-transparent-color.wp-block-button__link:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-transparent-color.wp-block-button__link:visited:not(:hover) {
  color: rgba(238, 238, 238, 0);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .wp-block-cover__inner-container,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .components-drop-zone + .wp-block-cover__inner-container,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-media-text:is(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color) > .wp-block-media-text__content {
  color: #fff;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .wp-block-cover__inner-container a:not(.wp-block-button__link),
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .components-drop-zone + .wp-block-cover__inner-container a:not(.wp-block-button__link),
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-media-text:is(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color) > .wp-block-media-text__content a:not(.wp-block-button__link) {
  color: #fff;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .wp-block-cover__inner-container .c-accordion__item.is-style-details .c-accordion__title,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .components-drop-zone + .wp-block-cover__inner-container .c-accordion__item.is-style-details .c-accordion__title,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-media-text:is(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color) > .wp-block-media-text__content .c-accordion__item.is-style-details .c-accordion__title {
  --color-accordion-link: #fff;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .wp-block-cover__inner-container .c-accordion__item.is-style-details .c-accordion__content,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color):is(.has-background-dim-80, .has-background-dim-90, .has-background-dim-100) + .components-drop-zone + .wp-block-cover__inner-container .c-accordion__item.is-style-details .c-accordion__content,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-media-text:is(.has-dias-primary-background-color, .has-dias-tertiary-background-color, .has-dias-black-background-color, .has-dias-base-background-color, .has-dias-grey-dark-background-color) > .wp-block-media-text__content .c-accordion__item.is-style-details .c-accordion__content {
  --color-accordion-text: #fff;
  --color-accordion-background: none;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-button__link:is(.has-dias-primary-background-color):not(.has-link-color) {
  --background: rgb(73.4, 117.4, 135.8);
  --background-hover: #1c536a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-button__link:is(.has-dias-white-background-color):not(.has-link-color) {
  --background: rgb(246.3, 250.6, 249.6);
  --background-hover: #fff;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-button__link:is(.has-dias-black-background-color):not(.has-link-color) {
  --background: #000;
  --background-hover: #000;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-button__link:is(.has-dias-secondary-background-color, .has-dias-white-background-color, .has-dias-grey-light-background-color):not(.has-link-color) {
  color: #1c536a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-button__link:is(.has-dias-secondary-background-color, .has-dias-white-background-color, .has-dias-grey-light-background-color):not(.has-link-color):is(:hover, :hover:visited, :focus) {
  color: #1c536a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-button__link.has-dias-transparent-background-color {
  background: transparent;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-outline .wp-block-button__link.has-dias-transparent-background-color {
  position: relative;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-outline .wp-block-button__link.has-dias-transparent-background-color:after {
  border: solid 1px white;
  content: "";
  opacity: 0;
  position: absolute;
  inset: 0;
  transition: opacity 0.2s ease-in-out;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-outline .wp-block-button__link.has-dias-transparent-background-color:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-outline .wp-block-button__link.has-dias-transparent-background-color:focus {
  border: solid 1px currentColor;
  color: currentColor;
  text-decoration: none;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-outline .wp-block-button__link.has-dias-transparent-background-color:hover:after, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-outline .wp-block-button__link.has-dias-transparent-background-color:focus:after {
  opacity: 1;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-outline .wp-block-button__link {
  border: solid 1px currentColor;
  transition: all 0.2s ease-in-out;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-black-border {
  border: solid 1px #1a1a1a;
  padding-bottom: 0.5rem;
  padding-top: 0.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container blockquote {
  background: rgb(209.6, 220.6, 225.2);
  border-left: 4px solid #1c536a;
  padding-left: 1.125rem;
  padding: 0.75rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-quote, .wp-block-pullquote) {
  font-style: normal;
  font-weight: 300;
  font-size: 28px;
  line-height: 36px;
  padding: 1.5rem;
  background: rgb(209.6, 220.6, 225.2);
  border-left: 4px solid #1c536a;
  padding: 1.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-primary-background-color {
  background: rgb(209.6, 220.6, 225.2) !important;
  border-left-color: #1c536a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-secondary-background-color {
  background: rgb(237.6, 246.2, 244.2) !important;
  border-left-color: #a8d3c9;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-tertiary-background-color {
  background: rgb(228.2, 208, 219.6) !important;
  border-left-color: #79144e;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-white-background-color {
  background: white !important;
  border-left-color: #fff;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-black-background-color {
  background: #cccccc !important;
  border-left-color: #000;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-grey-light-background-color {
  background: #f7f7f7 !important;
  border-left-color: #d7d7d7;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-grey-dark-background-color {
  background: rgb(209, 215.8, 215.4) !important;
  border-left-color: #193b39;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-base-background-color {
  background: rgb(209.2, 209.2, 209.2) !important;
  border-left-color: #1a1a1a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-highlight-background-color {
  background: rgb(253.2, 242, 218) !important;
  border-left-color: #f6be46;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-quote, .wp-block-pullquote).has-background.has-dias-transparent-background-color {
  background: rgba(255, 255, 255, 0.8) !important;
  border-left-color: rgba(238, 238, 238, 0);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-quote, .wp-block-pullquote) blockquote {
  border: 0;
  padding: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-quote, .wp-block-pullquote) blockquote p:first-child {
  margin-top: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .line-clamp-1,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .line-clamp-2,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .line-clamp-3,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .line-clamp-4,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .line-clamp-5 {
  --lines: 1;
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: var(--lines);
  -webkit-box-orient: vertical;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .line-clamp-2 {
  --lines: 2;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .line-clamp-3 {
  --lines: 3;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .line-clamp-4 {
  --lines: 4;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .line-clamp-5 {
  --lines: 5;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__item {
  background: rgb(232.3, 237.8, 240.1);
  border: 0;
  border-left: 4px solid #1c536a;
  box-shadow: 0px 4px 12px 0px rgba(21, 55, 53, 0.15);
  color: #1a1a1a;
  margin-bottom: 0.5rem;
  margin-top: 1.5rem;
  padding: 0;
  position: relative;
  transition: all 0.2s ease-in-out;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__item:hover {
  background: rgb(209.6, 220.6, 225.2);
  box-shadow: 0px 4px 16px 0px rgba(21, 55, 53, 0.4);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__item.is-open {
  border-bottom-width: 1px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__item + .c-accordion__item {
  margin-top: 2.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__title {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 24px;
  letter-spacing: 0;
  line-height: 28px;
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin: 0 1.5rem;
  padding: 1.5rem 0;
  position: static;
  outline: 0;
  text-decoration: none;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__title:hover {
  text-decoration: underline;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__title:focus-visible {
  background: #f6be46;
  box-shadow: 0 -2px #f6be46, 0 4px #1a1a1a;
  color: #1a1a1a !important;
  outline: 4px solid rgba(0, 0, 0, 0);
  text-decoration: none !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__title:after {
  background: #000;
  mask-image: url(../images/accordion.svg);
  mask-repeat: no-repeat;
  content: "";
  display: inline-block;
  height: 24px;
  margin-right: 0.5rem;
  position: relative;
  top: auto;
  transform: none;
  vertical-align: middle;
  width: 24px;
}
.is-open > .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__title:after {
  mask-image: url(../images/accordion-expanded.svg);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__content {
  margin: -1.5rem 0 0;
  padding: 1.5rem;
  position: relative;
  z-index: 2;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__content > p:first-child {
  margin-top: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-details {
  box-shadow: inset 4px 0 #d8dde0;
  margin-top: 8px;
  padding: 0 16px 0 20px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-details:open {
  padding: 0 16px 16px 20px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-details summary {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
  background: #fff;
  color: #1c536a;
  display: inline-block;
  margin-left: -20px;
  margin-top: -16px;
  padding: 0 0 0 24px;
  position: relative;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-details summary + * {
  margin-top: 12px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-details summary:focus-visible {
  background-color: #ffeb3b;
  box-shadow: 0 -2px #ffeb3b, 0 4px #212b32;
  color: #212b32;
  outline: 4px solid rgba(0, 0, 0, 0);
  text-decoration: none;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-details summary:before {
  background: none;
  border-style: solid;
  border-width: 7px 0 7px 12px;
  border-color: transparent transparent transparent currentColor;
  display: block;
  height: 0;
  left: 0;
  top: calc(50% - 10px);
  width: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__item.is-style-details {
  --color-accordion-link: #1c536a;
  --color-accordion-text: #1a1a1a;
  --color-accordion-background: none;
  background: none;
  border: 0;
  margin-top: 3rem;
  padding-bottom: 0;
  padding-top: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__item.is-style-details .c-accordion__title {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
  color: var(--color-accordion-link);
  display: inline-block;
  margin: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__item.is-style-details .c-accordion__title:focus-visible {
  background-color: #ffeb3b;
  box-shadow: 0 -2px #ffeb3b, 0 4px #212b32;
  color: #212b32;
  outline: 4px solid rgba(0, 0, 0, 0);
  text-decoration: none;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__item.is-style-details .c-accordion__title:after {
  display: none;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__item.is-style-details .c-accordion__title:before {
  background: none;
  mask-image: none;
  border-style: solid;
  border-width: 7px 0 7px 12px;
  border-color: transparent transparent transparent currentColor;
  display: inline-block;
  height: 0;
  margin-right: 0.75rem;
  margin-top: -8px;
  vertical-align: middle;
  width: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__item.is-style-details .c-accordion__content {
  background: var(--color-accordion-background);
  border-left: 4px solid #d8dde0;
  color: var(--color-accordion-text);
  margin-top: -1.5rem;
  padding: 1.5rem;
  padding-left: 20px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .c-accordion__item.is-style-details.is-open .c-accordion__title:before {
  transform: rotate(90deg);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-social-links:not(.is-style-logos-only) .wp-social-link {
  background: #1a1a1a;
  color: #fff;
  height: 40px;
  transition: all 0.2s ease-in-out;
  width: 40px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-social-links:not(.is-style-logos-only) .wp-social-link:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-social-links:not(.is-style-logos-only) .wp-social-link:focus {
  background: #1a1a1a;
  transform: none;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-social-links:not(.is-style-logos-only) .wp-social-link a {
  background: none;
  border-radius: 50%;
  height: 40px;
  position: relative;
  width: 40px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-social-links:not(.is-style-logos-only) .wp-social-link a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-social-links:not(.is-style-logos-only) .wp-social-link a:focus {
  background-size: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-social-links:not(.is-style-logos-only) .wp-social-link a:focus-visible {
  background: #1a1a1a;
  color: #fff !important;
  outline: #000 auto 5px;
  border: solid 4px #f6be46;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-social-links:not(.is-style-logos-only) .wp-social-link a svg {
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container ul,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container ol {
  list-style-position: outside;
  list-style-type: disc;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container ul li,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container ol li {
  margin-left: 1.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container ul li:where(:not(:last-child)),
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container ol li:where(:not(:last-child)) {
  margin-bottom: 0.75rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container ol {
  list-style: decimal;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-separator:not(.is-style-dots) {
  border-bottom: 1px solid rgb(94.7, 94.7, 94.7);
  margin-bottom: 2.5rem;
  margin-top: 2.5rem;
  opacity: 0.5;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-separator:not(.is-style-dots).is-style-wide {
  margin-top: 72px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-separator:not(.is-style-dots):first-child {
  margin-top: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-separator:not(.is-style-dots).has-background:not(.is-style-dots) {
  border-top-style: solid;
  border-top-width: 1px;
  border-color: currentColor;
  height: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-social-link a {
  background: none;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-background :where(a) {
  color: currentColor;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-footnotes {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px !important;
  line-height: 24px !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container sup.fn a {
  padding: 0 3px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container sup.fn::before {
  content: "(";
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container sup.fn::after {
  content: ")";
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > *, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .gform_legacy_markup_wrapper, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block-image, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block-table, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block-group, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block-cover.aligncenter,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.alignfull > *, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .acf-block-body > *, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block-separator:not(.is-style-wide):not(.is-style-dots),
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover__inner-container > * {
  --wp--style--global--content-size: 51.25rem;
  margin-left: auto;
  margin-right: auto;
  max-width: 51.25rem;
  width: calc(100% - var(--side-gutter) * 2);
}
:where(body.single-snapshot) .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > *, :where(body.single-snapshot) .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .gform_legacy_markup_wrapper, :where(body.single-snapshot) .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block, :where(body.single-snapshot) .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block-image, :where(body.single-snapshot) .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block-table, :where(body.single-snapshot) .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block-group, :where(body.single-snapshot) .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block-cover.aligncenter,
:where(body.single-snapshot) .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.alignfull > *, :where(body.single-snapshot) .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .acf-block-body > *, :where(body.single-snapshot) .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block-separator:not(.is-style-wide):not(.is-style-dots),
:where(body.single-snapshot) .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover__inner-container > * {
  max-width: 960px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide {
  container-type: inline-size;
  container-name: content-size;
  margin: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > *:not(.alignfull, .wp-block-cover), .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > .gform_legacy_markup_wrapper:not(.alignfull, .wp-block-cover), .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > .wp-block:not(.alignfull, .wp-block-cover), .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > .wp-block-image:not(.alignfull, .wp-block-cover), .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > .wp-block-table:not(.alignfull, .wp-block-cover), .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > .wp-block-group:not(.alignfull, .wp-block-cover),
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide .wp-block-group.alignfull > *:not(.alignfull, .wp-block-cover), .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > .acf-block-body > *:not(.alignfull, .wp-block-cover), .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > .wp-block-separator:not(.is-style-wide):not(.is-style-dots):not(.alignfull, .wp-block-cover),
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide .wp-block-cover__inner-container > *:not(.alignfull, .wp-block-cover) {
  margin-left: var(--side-gutter);
}
@container content-size (width > 1224px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > *:not(.alignfull, .wp-block-cover), .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > .gform_legacy_markup_wrapper:not(.alignfull, .wp-block-cover), .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > .wp-block:not(.alignfull, .wp-block-cover), .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > .wp-block-image:not(.alignfull, .wp-block-cover), .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > .wp-block-table:not(.alignfull, .wp-block-cover), .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > .wp-block-group:not(.alignfull, .wp-block-cover),
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide .wp-block-group.alignfull > *:not(.alignfull, .wp-block-cover), .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > .acf-block-body > *:not(.alignfull, .wp-block-cover), .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide > .wp-block-separator:not(.is-style-wide):not(.is-style-dots):not(.alignfull, .wp-block-cover),
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container.entry-content--wide .wp-block-cover__inner-container > *:not(.alignfull, .wp-block-cover) {
    margin-left: calc((100% - 75rem) / 2);
  }
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-column) .wp-block-cover {
  padding: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-column) .wp-block-cover__inner-container {
  padding: 1.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.wp-block-column) .wp-block-cover__inner-container > * {
  margin-left: auto !important;
  width: auto;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > iframe {
  display: block;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover {
  padding-right: 0;
  padding-left: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container [data-align=full],
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .alignfull,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-full {
  --wp--style--global--content-size: none;
  max-width: none;
  width: 100%;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container [data-align=wide],
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .alignwide,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-wide,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover__inner-container .alignwide,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-floating-image {
  --wp--style--global--content-size: 75rem;
  max-width: 75rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover-image {
  min-height: 300px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.wp-block-cover {
  border-radius: 4px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block-cover.wp-block-cover {
  padding-bottom: 72px;
  padding-top: 72px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block-cover.wp-block-cover:not(.aligncenter, .alignwide) {
  border-radius: 0;
}
@media (max-width: 678px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-group.has-background) {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block[data-align=center] .wp-block-cover,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block-cover.aligncenter {
  padding: 1.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block[data-align=center] .wp-block-cover > .wp-block-cover__inner-container > *,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container > .wp-block-cover.aligncenter > .wp-block-cover__inner-container > * {
  width: auto;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-preformatted {
  background-color: #153634;
  border-left: 11px solid;
  border-radius: 8px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  color: #ffffff;
  font-size: 14px;
  padding: 0.75rem 1.125rem;
  line-height: 1.3;
  white-space: pre-wrap;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container mark.has-inline-color {
  transition: color 0.2s ease-out 0.5s;
}
@media (min-width: 1351px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group:has(> .wp-block-cover.is-style-card-link) {
    gap: 72px;
  }
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link {
  flex: 0 1 50%;
  max-width: 352px;
  min-width: 220px;
  min-height: 220px;
  padding: 3rem 1.5rem;
  position: relative;
  overflow: visible;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link a:before {
  content: "";
  inset: 0;
  position: absolute;
  z-index: 2;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link > .wp-block-cover__background {
  border: solid 1px var(--border-color, transparent);
  opacity: 1;
  transition: background 0.2s ease-out;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__inner-container {
  text-align: center;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__inner-container > * {
  --side-gutter: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__inner-container .wp-block-heading {
  margin-top: 0.75rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__inner-container ul.is-style-list-style-none,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__inner-container ol.is-style-list-style-none {
  margin-top: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__inner-container :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  transform: scale(var(--scale, 1));
  transition: transform 0.2s ease-out;
  transform-origin: center bottom;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-primary-background-color {
  --border-color: rgb(141.5, 169, 180.5);
  --background: rgb(198.25, 212, 217.75);
  opacity: 1;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-secondary-background-color {
  --border-color: rgb(211.5, 233, 228);
  --background: rgb(233.25, 244, 241.5);
  opacity: 1;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-tertiary-background-color {
  --border-color: rgb(188, 137.5, 166.5);
  --background: rgb(221.5, 196.25, 210.75);
  opacity: 1;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-white-background-color {
  --border-color: white;
  --background: white;
  opacity: 1;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-black-background-color {
  --border-color: rgb(127.5, 127.5, 127.5);
  --background: rgb(191.25, 191.25, 191.25);
  opacity: 1;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-grey-light-background-color {
  --border-color: #ebebeb;
  --background: whitesmoke;
  opacity: 1;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-grey-dark-background-color {
  --border-color: #8c9d9c;
  --background: rgb(197.5, 206, 205.5);
  opacity: 1;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-base-background-color {
  --border-color: rgb(140.5, 140.5, 140.5);
  --background: rgb(197.75, 197.75, 197.75);
  opacity: 1;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-highlight-background-color {
  --border-color: rgb(250.5, 222.5, 162.5);
  --background: rgb(252.75, 238.75, 208.75);
  opacity: 1;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link .wp-block-cover__background.has-dias-transparent-background-color {
  --border-color: rgba(255, 255, 255, 0.5);
  --background: rgba(255, 255, 255, 0.75);
  opacity: 1;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-primary-background-color {
  --border-color: rgb(84.75, 126, 143.25);
  --background: rgb(141.5, 169, 180.5);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-secondary-background-color {
  --border-color: rgb(189.75, 222, 214.5);
  --background: rgb(211.5, 233, 228);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-tertiary-background-color {
  --border-color: rgb(154.5, 78.75, 122.25);
  --background: rgb(188, 137.5, 166.5);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-white-background-color {
  --border-color: white;
  --background: white;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-black-background-color {
  --border-color: rgb(63.75, 63.75, 63.75);
  --background: rgb(127.5, 127.5, 127.5);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-grey-light-background-color {
  --border-color: #e1e1e1;
  --background: #ebebeb;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-grey-dark-background-color {
  --border-color: rgb(82.5, 108, 106.5);
  --background: #8c9d9c;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-base-background-color {
  --border-color: rgb(83.25, 83.25, 83.25);
  --background: rgb(140.5, 140.5, 140.5);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-highlight-background-color {
  --border-color: rgb(248.25, 206.25, 116.25);
  --background: rgb(250.5, 222.5, 162.5);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) .wp-block-cover__background.has-dias-transparent-background-color {
  --border-color: rgba(255, 255, 255, 0.25);
  --background: rgba(255, 255, 255, 0.5);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-cover.is-style-card-link:has(a:hover, a:focus) :is(img[style*="width:36px"], img[style*="width: 36px"]) {
  --scale: 1.1666;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-bordered {
  border-left: 4px solid #1c536a;
  padding-left: 1.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered {
  border-left: 4px solid #1c536a;
  background: rgb(232.3, 237.8, 240.1);
  padding: 1.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered .wp-block-heading:not(.has-text-color) {
  color: rgb(22.4, 66.4, 84.8);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container ul.is-style-bordered {
  padding: 0 0 0 1.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-primary-background-color + .wp-block-cover__inner-container) .is-style-bordered,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-primary-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #1c536a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-secondary-background-color + .wp-block-cover__inner-container) .is-style-bordered,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-secondary-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #a8d3c9;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-tertiary-background-color + .wp-block-cover__inner-container) .is-style-bordered,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-tertiary-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #79144e;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-white-background-color + .wp-block-cover__inner-container) .is-style-bordered,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-white-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #fff;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-black-background-color + .wp-block-cover__inner-container) .is-style-bordered,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-black-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #000;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-grey-light-background-color + .wp-block-cover__inner-container) .is-style-bordered,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-grey-light-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #d7d7d7;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-grey-dark-background-color + .wp-block-cover__inner-container) .is-style-bordered,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-grey-dark-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #193b39;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-base-background-color + .wp-block-cover__inner-container) .is-style-bordered,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-base-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #1a1a1a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-highlight-background-color + .wp-block-cover__inner-container) .is-style-bordered,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-highlight-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: #f6be46;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-transparent-background-color + .wp-block-cover__inner-container) .is-style-bordered,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :where(.wp-block-cover__background.has-dias-transparent-background-color + .components-drop-zone + .wp-block-cover__inner-container) .is-style-bordered {
  border-left-color: rgba(238, 238, 238, 0);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-bordered.has-dias-primary-background-color {
  border-left-color: #1c536a;
  background: transparent !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-primary-background-color {
  background: rgb(232.3, 237.8, 240.1) !important;
  border-left-color: #1c536a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-primary-background-color .wp-block-heading:not(.has-text-color) {
  color: rgb(22.4, 66.4, 84.8);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-bordered.has-dias-secondary-background-color {
  border-left-color: #a8d3c9;
  background: transparent !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-secondary-background-color {
  background: rgb(246.3, 250.6, 249.6) !important;
  border-left-color: #a8d3c9;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-secondary-background-color .wp-block-heading:not(.has-text-color) {
  color: rgb(134.4, 168.8, 160.8);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-bordered.has-dias-tertiary-background-color {
  border-left-color: #79144e;
  background: transparent !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-tertiary-background-color {
  background: rgb(241.6, 231.5, 237.3) !important;
  border-left-color: #79144e;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-tertiary-background-color .wp-block-heading:not(.has-text-color) {
  color: rgb(96.8, 16, 62.4);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-bordered.has-dias-white-background-color {
  border-left-color: #fff;
  background: transparent !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-white-background-color {
  background: white !important;
  border-left-color: #fff;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-white-background-color .wp-block-heading:not(.has-text-color) {
  color: #cccccc;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-bordered.has-dias-black-background-color {
  border-left-color: #000;
  background: transparent !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-black-background-color {
  background: rgb(229.5, 229.5, 229.5) !important;
  border-left-color: #000;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-black-background-color .wp-block-heading:not(.has-text-color) {
  color: black;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-bordered.has-dias-grey-light-background-color {
  border-left-color: #d7d7d7;
  background: transparent !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-grey-light-background-color {
  background: #fbfbfb !important;
  border-left-color: #d7d7d7;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-grey-light-background-color .wp-block-heading:not(.has-text-color) {
  color: #acacac;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-bordered.has-dias-grey-dark-background-color {
  border-left-color: #193b39;
  background: transparent !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-grey-dark-background-color {
  background: rgb(232, 235.4, 235.2) !important;
  border-left-color: #193b39;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-grey-dark-background-color .wp-block-heading:not(.has-text-color) {
  color: rgb(20, 47.2, 45.6);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-bordered.has-dias-base-background-color {
  border-left-color: #1a1a1a;
  background: transparent !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-base-background-color {
  background: rgb(232.1, 232.1, 232.1) !important;
  border-left-color: #1a1a1a;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-base-background-color .wp-block-heading:not(.has-text-color) {
  color: rgb(20.8, 20.8, 20.8);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-bordered.has-dias-highlight-background-color {
  border-left-color: #f6be46;
  background: transparent !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-highlight-background-color {
  background: rgb(254.1, 248.5, 236.5) !important;
  border-left-color: #f6be46;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-highlight-background-color .wp-block-heading:not(.has-text-color) {
  color: rgb(196.8, 152, 56);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-bordered.has-dias-transparent-background-color {
  border-left-color: rgba(238, 238, 238, 0);
  background: transparent !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-transparent-background-color {
  background: rgba(255, 255, 255, 0.9) !important;
  border-left-color: rgba(238, 238, 238, 0);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-bordered.has-dias-transparent-background-color .wp-block-heading:not(.has-text-color) {
  color: rgba(0, 0, 0, 0.2);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-boxed {
  background: rgb(232.3, 237.8, 240.1);
  border: 1px solid rgb(141.5, 169, 180.5);
  min-height: 260px;
  padding: 1.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-boxed.has-dias-primary-background-color {
  background-color: rgb(232.3, 237.8, 240.1) !important;
  border-color: rgb(141.5, 169, 180.5);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-boxed.has-dias-secondary-background-color {
  background-color: rgb(246.3, 250.6, 249.6) !important;
  border-color: rgb(211.5, 233, 228);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-boxed.has-dias-tertiary-background-color {
  background-color: rgb(241.6, 231.5, 237.3) !important;
  border-color: rgb(188, 137.5, 166.5);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-boxed.has-dias-white-background-color {
  background-color: white !important;
  border-color: white;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-boxed.has-dias-black-background-color {
  background-color: rgb(229.5, 229.5, 229.5) !important;
  border-color: rgb(127.5, 127.5, 127.5);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-boxed.has-dias-grey-light-background-color {
  background-color: #fbfbfb !important;
  border-color: #ebebeb;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-boxed.has-dias-grey-dark-background-color {
  background-color: rgb(232, 235.4, 235.2) !important;
  border-color: #8c9d9c;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-boxed.has-dias-base-background-color {
  background-color: rgb(232.1, 232.1, 232.1) !important;
  border-color: rgb(140.5, 140.5, 140.5);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-boxed.has-dias-highlight-background-color {
  background-color: rgb(254.1, 248.5, 236.5) !important;
  border-color: rgb(250.5, 222.5, 162.5);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-boxed.has-dias-transparent-background-color {
  background-color: rgba(255, 255, 255, 0.9) !important;
  border-color: rgba(255, 255, 255, 0.5);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-media-text.is-image-fill-element .wp-block-media-text__media {
  min-height: 100%;
  position: relative;
}
@media (max-width: 600px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-media-text.is-image-fill-element .wp-block-media-text__media {
    min-height: 220px;
  }
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-media-text.is-image-fill-element .wp-block-media-text__media img {
  display: block;
  height: 100%;
  margin: 0;
  position: absolute;
  object-fit: cover;
  transform: scale(1);
  transition: all 0.2s ease;
  width: 100%;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .button {
  align-items: flex-start;
  background: var(--background, rgb(73.4, 117.4, 135.8));
  border-radius: 0;
  border: 0;
  color: #fff;
  cursor: pointer;
  display: inline-flex;
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 18px;
  padding: 0.75rem 2rem;
  text-align: center;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .button:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .button:hover:visited, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .button:focus {
  background: var(--background-hover, #1c536a);
  outline: none;
  text-decoration: underline;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .button:focus-visible {
  box-shadow: inset 0px 0px 0 2px #000;
  outline: 4px solid #f6be46 !important;
  outline-offset: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-columns.is-style-space-between {
  justify-content: space-between;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-columns.alignwide:has(.wp-block-column[style*="66.66%"]):has(.wp-block-column[style*="33.33%"]),
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block[data-align=wide] > .wp-block-columns:has(> .wp-block-column[style*="66.66%"]):has(> .wp-block-column[style*="33.33%"]) {
  justify-content: space-between;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-columns.alignwide:has(.wp-block-column[style*="66.66%"]):has(.wp-block-column[style*="33.33%"]) > .wp-block-column[style*="66.66%"],
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block[data-align=wide] > .wp-block-columns:has(> .wp-block-column[style*="66.66%"]):has(> .wp-block-column[style*="33.33%"]) > .wp-block-column[style*="66.66%"] {
  flex-basis: 51.25rem !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-columns.alignwide:has(.wp-block-column[style*="66.66%"]):has(.wp-block-column[style*="33.33%"]) > .wp-block-column[style*="33.33%"],
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block[data-align=wide] > .wp-block-columns:has(> .wp-block-column[style*="66.66%"]):has(> .wp-block-column[style*="33.33%"]) > .wp-block-column[style*="33.33%"] {
  flex-basis: 368px !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container img.alignleft {
  float: left;
  margin: 5px 1.5rem 1.5rem 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container img.alignright {
  float: right;
  margin: 5px 0 1.5rem 1.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-list-style-none {
  list-style: none;
  margin-left: 0;
  padding-left: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-list-style-none li {
  margin: 0 0 0;
  padding: 0 0 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-style-list-style-none li:not(:last-child) {
  margin-bottom: 0.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .mt-auto {
  margin-top: auto !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .h-full {
  height: 100%;
}
@media (min-width: 782px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-column > .wp-block-cover.is-style-sticky {
    position: sticky;
    top: 1.5rem;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-column > .wp-block-cover.is-style-sticky > .wp-block-cover__inner-container:not(:has(> .in-page-sub-nav)) {
    max-height: calc(100vh - 48px);
    max-height: calc(100dvh - 48px);
    overflow: auto;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-column > .wp-block-cover.is-style-sticky > .wp-block-cover__inner-container > .in-page-sub-nav > ul {
    max-height: calc(100vh - 150px);
    max-height: calc(100dvh - 150px);
    overflow: auto;
  }
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .social-sharing {
  align-items: center;
  display: flex;
  gap: 1.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .social-sharing > p {
  margin: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .social-sharing-list {
  align-items: center;
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  list-style: none;
  margin: 0;
  padding: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .social-sharing-list > li {
  margin: 0;
  padding: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .social-sharing-list svg {
  height: 28px;
  width: 28px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .social-sharing-list svg path {
  fill: currentColor;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .social-sharing-list a {
  color: #000;
  display: block;
  position: relative;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .social-sharing-list a:hover,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .social-sharing-list a:focus {
  color: #1c536a;
  transform: scale(1.1);
  transition: all 0.2s ease-out;
}
body.single-snapshot .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .social-sharing-list a:hover,
body.single-snapshot .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .social-sharing-list a:focus {
  color: #79144e;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav {
  background: var(--subnav-bg, #1c536a);
  color: #fff;
  max-width: none;
  position: sticky;
  top: var(--wp-admin--admin-bar--height, 0);
  width: 100%;
  z-index: 20;
}
@media screen and (max-width: 600px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav {
    top: 0;
  }
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav > ul {
  display: flex;
  justify-content: flex-start;
  list-style: none;
  margin: 0 auto;
  max-width: 51.25rem;
  overflow: auto;
  padding: 0;
  white-space: nowrap;
}
:where(body.single-snapshot) .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav > ul {
  max-width: 960px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav > ul > li {
  margin: 0;
  padding: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav > ul > li > a {
  color: #fff;
  display: block;
  padding: 0.75rem 1.125rem;
  text-decoration: none;
  white-space: nowrap;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav > ul > li > a.current, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav > ul > li > a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav > ul > li > a:focus {
  background: var(--subnav-hover, rgb(73.4, 117.4, 135.8));
  text-decoration: underline;
}
@media (max-width: 699px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav > ul > li a.wp-block-button__link {
    line-height: 28px;
    padding-bottom: 0.75rem;
    padding-top: 0.75rem;
  }
}
.single-snapshot .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav {
  --subnav-bg: rgb(84.7, 14, 54.6);
  --subnav-hover: rgb(147.8, 67, 113.4);
}
@media (min-width: 700px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav--left {
    background: none;
    color: #000;
    margin-left: 1.5rem;
    width: 228px;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav--left > ul {
    background: none;
    flex-direction: column;
    margin-top: 104px;
    position: absolute;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav--left > ul::before {
    font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 32px;
    letter-spacing: 0;
    line-height: 40px;
    content: "Contents";
    margin-bottom: 1.5rem;
  }
}
@media (min-width: 700px) and (max-width: 600px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav--left > ul::before {
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    letter-spacing: 0;
    line-height: 30px;
  }
}
@media (min-width: 700px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav--left > ul > li {
    margin: 0;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav--left > ul > li:first-child {
    margin: 0;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav--left > ul > li > a {
    font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif;
    font-style: normal;
    font-weight: 300;
    font-size: 16px !important;
    line-height: 24px !important;
    background: none;
    color: #000;
    padding: 0 0.75rem 0.75rem;
    position: relative;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav--left > ul > li > a:after {
    background: var(--border-colour, transparent);
    content: "";
    inset: 0 0 0 auto;
    position: absolute;
    width: 5px;
    transition: background 0.2s ease-out;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav--left > ul > li > a.current, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav--left > ul > li > a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav--left > ul > li > a:focus {
    background: none;
    --border-colour: blue;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav--left > ul .wp-block-buttons {
    flex-direction: column;
    margin-top: 1.5rem;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .in-page-sub-nav--left {
    margin-left: max((100vw - 51.25rem) / 2 - 228px, var(--side-gutter));
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .two-column-layout {
    margin-left: max((100vw - 51.25rem) / 2 - 228px - 48px, var(--side-gutter));
  }
}
@media (max-width: 699px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .two-column-layout > *:has(.in-page-sub-nav) {
    position: sticky;
    top: 0;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .two-column-layout .in-page-sub-nav {
    margin-left: calc(var(--side-gutter) * -1);
    margin-right: calc(var(--side-gutter) * -1);
    width: auto;
  }
}
@media (min-width: 700px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .two-column-layout {
    display: grid;
    grid-template-columns: 220px 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
    margin-top: 3rem;
    max-width: calc(51.25rem + 220px);
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .two-column-layout .in-page-sub-nav--left {
    margin-left: 0;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .two-column-layout .in-page-sub-nav--left > ul {
    position: relative;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .two-column-layout .in-page-sub-nav--left > ul {
    margin-top: 1.5rem;
  }
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .two-column-issue-layout {
  gap: 1.5rem;
  justify-content: space-between;
  max-width: 75rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .issue-main-sidebar {
  padding-top: 1.5rem;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .subnav-issue-paper {
  max-height: 100vh;
  overflow: auto;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .subnav-issue-paper > ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .subnav-issue-paper > ul > li {
  margin: 0 0 4px;
  padding: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .subnav-issue-paper > ul > li > a {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
  display: block;
  padding: 0.75rem 1.125rem;
  position: relative;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .subnav-issue-paper > ul > li > a::before {
  background: var(--border-colour, transparent);
  content: "";
  inset: 0 auto 0 0;
  position: absolute;
  transition: background 0.2s ease-out;
  width: 4px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .subnav-issue-paper > ul > li > a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .subnav-issue-paper > ul > li > a:focus {
  text-decoration: underline;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .subnav-issue-paper > ul > li > a.current {
  --border-colour: #000;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .subnav-issue-paper .wp-block-buttons {
  align-items: flex-start;
  flex-direction: column;
  margin-top: 1.5rem;
}
@media (min-width: 700px) {
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .two-column-issue-layout {
    display: flex;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .issue-main-content {
    flex: 0 1 820px;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .issue-main-sidebar {
    flex: 0 0 308px;
    max-width: 308px;
  }
  .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .subnav-issue-paper {
    position: sticky;
    top: 0;
  }
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .is-content-justification-space-between {
  justify-content: space-between;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .sort-by-field {
  align-items: center;
  display: flex;
  gap: 0.5rem;
  white-space: nowrap;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .sort-by-field label {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .featured-item__label {
  text-transform: uppercase;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .featured-item__label:before {
  mask: url(../images/bulletin.svg) no-repeat center;
  background-color: currentColor;
  display: inline-block;
  content: '"';
  height: 18px;
  margin: 0 4px 0 0;
  position: relative;
  vertical-align: baseline;
  width: 18px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .featured-item--snapshot .featured-item__label::before {
  mask-image: url(../images/snapshot.svg);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .featured-item--issue-paper .featured-item__label::before {
  mask-image: url(../images/issue-paper.svg);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .highcharts-container {
  width: auto !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .highcharts-root {
  font-family: Montserrat, "Montserrat", "Franklin Gothic Medium", Tahoma, sans-serif !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .highcharts-root .highcharts-text-outline,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .highcharts-root tspan[style="font-weight: bold;"] {
  font-weight: 300 !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .highcharts-root tspan[style="font-style: italic;"] {
  font-weight: 600 !important;
  font-style: normal !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container :is(.highcharts-yaxis-labels, .highcharts-yaxis-labels text, .highcharts-xaxis-labels, .highcharts-xaxis-labels text, .highcharts-axis-title, .highcharts-data-label tspan) {
  font-size: 14px !important;
  font-weight: 300 !important;
  line-height: 16px !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .highcharts-crosshair.highcharts-crosshair-category {
  stroke: rgba(121, 20, 78, 0.1) !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container #listing {
  transition: opacity 0.3s ease-in;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container #listing.listing-loading {
  opacity: 0.3;
  transition: opacity 0.1s ease-out;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .item-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  list-style: none;
  max-width: 75rem;
  padding: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .item-cards > li {
  margin: 0;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .item-card {
  display: grid;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .item-card__title {
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 24px;
  letter-spacing: 0;
  line-height: 28px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .item-card a {
  border: 1px solid rgb(211.5, 233, 228);
  background: rgb(233.25, 244, 241.5);
  color: #000;
  display: grid;
  justify-content: center;
  align-items: center;
  min-height: 144px;
  padding: 1.5rem;
  text-align: center;
  transition: border-color 0.2s ease-out, background 0.2s ease-out, color 0.2s ease-out;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .item-card a:hover {
  border-color: rgb(185.4, 219.8, 211.8);
  background: rgb(211.5, 233, 228);
  color: #000;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .item-card--snapshot a {
  border-color: rgb(188, 137.5, 166.5);
  background: rgb(241.6, 231.5, 237.3);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .item-card--snapshot a:hover, .is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .item-card--snapshot a:focus {
  border-color: rgb(147.8, 67, 113.4);
  background: rgb(228.2, 208, 219.6);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-primary-background-color {
  background: var(--background, #1c536a) !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-secondary-background-color {
  background: var(--background, #a8d3c9) !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-tertiary-background-color {
  background: var(--background, #79144e) !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-white-background-color {
  background: var(--background, #fff) !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-black-background-color {
  background: var(--background, #000) !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-light-background-color {
  background: var(--background, #d7d7d7) !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-grey-dark-background-color {
  background: var(--background, #193b39) !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-base-background-color {
  background: var(--background, #1a1a1a) !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-highlight-background-color {
  background: var(--background, #f6be46) !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .has-dias-transparent-background-color {
  background: var(--background, rgba(238, 238, 238, 0)) !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .related-pages__pagination {
  width: calc(100% - 60px);
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .related-pages__list {
  width: 100%;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .wp-block-group.is-style-carousel > * {
  flex: 1 1 auto;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .acf-block-body {
  max-width: none !important;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .flourish-embed {
  border: solid 1px #EDF0F7;
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: linear-gradient(45deg, #EDF0F7 25%, transparent 25%, transparent 75%, #EDF0F7 75%, #EDF0F7), linear-gradient(45deg, #EDF0F7 25%, transparent 25%, transparent 75%, #EDF0F7 75%, #EDF0F7);
  min-height: 200px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .flourish-embed::before {
  content: "Flourish embed";
  font-size: 20px;
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .flourish-embed::after {
  font-size: 12px;
  content: "content can only render in the front end";
}
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .sr-only,
.is-root-container.is-root-container.is-root-container.is-root-container.is-root-container .screen-reader-text {
  clip: rect(1px, 1px, 1px, 1px);
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
  word-wrap: normal !important;
}

.block-editor__typewriter > div {
  max-width: none;
  width: auto;
}

.is-root-container .wp-block-cover__inner-container > .block-editor-block-list__block {
  max-width: 75rem;
  width: auto;
}
.is-root-container .wp-block-cover__inner-container > .block-editor-block-list__block > .wp-block {
  max-width: none;
}
.is-root-container .wp-block-cover__inner-container > .block-editor-block-list__block > .wp-block[data-align=wide] {
  max-width: 75rem;
}
.is-root-container .wp-block-cover__inner-container > .block-editor-block-list__block > .wp-block[data-align=left], .is-root-container .wp-block-cover__inner-container > .block-editor-block-list__block > .wp-block[data-align=right] {
  height: auto;
}
.is-root-container .wp-block-cover__inner-container > .block-editor-block-list__block > .wp-block[data-align=left] > *, .is-root-container .wp-block-cover__inner-container > .block-editor-block-list__block > .wp-block[data-align=right] > * {
  float: none;
  margin-left: unset;
  margin-right: unset;
}

.is-root-container .project-metadata {
  position: relative !important;
  right: auto !important;
  width: auto !important;
}
.is-root-container .wp-block[data-type="dias/news"] {
  max-width: none !important;
  width: auto !important;
}

.is-root-container .wp-block-acf-members-listing {
  max-width: none !important;
}
.is-root-container .wp-block-acf-members-listing .member-list {
  max-width: 51.25rem;
  margin-left: auto;
  margin-right: auto;
}
.is-root-container .acf-block-component.acf-block-preview:after {
  background: transparent;
  bottom: 0;
  content: "";
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 20;
}
.is-root-container .acf-block-component .acf-innerblocks-container {
  position: relative;
  z-index: 30;
}
.is-root-container .acf-block-preview.acf-block-preview.acf-block-preview.acf-block-preview {
  z-index: 1;
}

body.interim-login {
  background: #fff;
}

body.login {
  background: #1c536a;
  display: grid;
  place-content: center;
}
body.login #login {
  background: #fff;
  border: #1c536a 1px solid;
  margin: 0 0 20px;
  max-width: 30rem;
  padding: 0;
  width: calc(100% - 40px);
}
body.login #login #backtoblog,
body.login #login .privacy-policy-page-link,
body.login #login h1 a {
  display: none;
}
body.login #login #nav {
  padding: 0 1.5rem 1.5rem;
}
body.login #login h1 {
  background: #fff url(../images/logo.svg) 50% 50% no-repeat;
  width: 374px;
  height: 143px;
  background-size: contain;
  margin: 3rem auto 0;
  border: solid 20px #fff;
  border-width: 0 20px;
}
body.login #login form {
  border: 0;
  margin: 0;
}
body.login #login .message {
  border-left: none;
  text-align: center;
  box-shadow: none;
  margin: 0;
  padding-top: 30px;
}
body.login #wp-submit {
  align-items: flex-start;
  background: var(--background, rgb(73.4, 117.4, 135.8));
  border-radius: 0;
  border: 0;
  color: #fff;
  cursor: pointer;
  display: inline-flex;
  font-family: "Noticia Text", "Franklin Gothic Medium", Tahoma, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 18px;
  padding: 0.75rem 2rem;
  text-align: center;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
  margin-top: 2rem;
}
body.login #wp-submit:hover, body.login #wp-submit:hover:visited, body.login #wp-submit:focus {
  background: var(--background-hover, #1c536a);
  outline: none;
  text-decoration: underline;
}
body.login #wp-submit:focus-visible {
  box-shadow: inset 0px 0px 0 2px #000;
  outline: 4px solid #f6be46 !important;
  outline-offset: 0;
}