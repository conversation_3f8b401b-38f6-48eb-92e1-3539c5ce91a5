!function n(e,o,u){function a(i,t){if(!o[i]){if(!e[i]){var r="function"==typeof require&&require;if(!t&&r)return r(i,!0);if(s)return s(i,!0);throw new Error("Cannot find module '"+i+"'")}t=o[i]={exports:{}};e[i][0].call(t.exports,function(t){var r=e[i][1][t];return a(r||t)},t,t.exports,n,e,o,u)}return o[i].exports}for(var s="function"==typeof require&&require,t=0;t<u.length;t++)a(u[t]);return a}({1:[function(t,r,i){"use strict";var n;(n=jQuery)(function(){var t=n('textarea[name="cap-description"], textarea#description');t.length&&t.each(function(){var t=n(this);t.attr("id")||t.attr("id","bio-"+Math.random().toString(36).substr(2,9)),window.tinymce.init({selector:"#"+t.attr("id"),plugins:"lists link",toolbar:"bold italic underline strikethrough | bullist numlist | link",menubar:!1,width:"100%",branding:!1})})})},{}]},{},[1]);