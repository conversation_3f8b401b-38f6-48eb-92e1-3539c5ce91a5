function debounce(n,a=300,i=!1){let o=null;return function(){var t=i&&!o,e=()=>{n.apply(this,arguments),o=null};clearTimeout(o),o=setTimeout(e,a),t&&e()}}window.addEventListener("load",function(){var t=document.querySelector("#wp-admin-bar-new-content > a");t&&t.addEventListener("click",function(t){return t.preventDefault(),t.stopPropagation(),!1})}),function(r){const n=r(".js-ajax");function i(t){const e=r("#listing-pagination"),n=r("#listing");n.addClass("listing-loading");let a=0;const i=setInterval(()=>{300<=(a+=100)&&clearInterval(i)},100);r.get(t,function(t){t=r(t),n.html(t.find("#listing").html()||""),e.length&&e.html(t.find("#listing-pagination").html()||""),void 0!==window.runAOS&&window.runAOS(),setTimeout(()=>{n.offset().top<r(window).scrollTop()&&r("html, body").animate({scrollTop:n.offset().top-100},600),n.removeClass("listing-loading")},300-a),t=t.find(".js-update-with-ajax-load");console.log(t),t.length&&t.each(function(){var t=r(this),e=t.attr("id"),e=r("#"+e);e.length&&e.html(t.html())});try{window.lazyLoadOptions&&"undefined"!=typeof LazyLoad&&new LazyLoad(window.lazyLoadOptions)}catch(t){console.log("w3 lazyload fail")}})}n.length&&(r("body").on("click",".button-group label",function(t){const e=r(this).prev("input");e.prop("checked")&&setTimeout(()=>{e.prop("checked",!1),e.trigger("change")},0)}),r("body").on("click","a.js-filter",function(t){t.preventDefault();var e=this.href;let a=[];try{a=e.split("?")[1].split("&").map(t=>{var[t,e]=t.split("=");let n=decodeURIComponent(t);return{name:n=n.replace(/\[\d+\]/gi,"[]"),value:e}})}catch(t){a=[]}n[0].querySelectorAll("input, select").forEach(function(t){t=r(t);const e=t.attr("name"),n=t.val();a.filter(t=>t.name===e&&t.value===n).length?["checkbox","radio"].includes(t.attr("type"))&&!t.prop("checked")&&(t.prop("checked",!0),t.trigger("change")):["checkbox","radio"].includes(t.attr("type"))?t.prop("checked")&&(t.prop("checked",!1),t.trigger("change")):t.val("")}),history.pushState({submitUrl:e,paramsArray:a,urlToLoad:e},"",e),i(e)}),r("#listing-pagination").on("click","a",function(t){t.preventDefault();var t=this.href,e=r(n[0]).serializeArray();history.pushState({submitUrl:t,paramsArray:e,urlToLoad:t},"",t),i(t)}),n.each(function(){const a=r(this);a.find('[type="submit"]:not(.js-always-visible)').hide();a.find('[type="submit"].js-always-visible').on("click",()=>{var t=r("#listing");t.length&&r("html, body").animate({scrollTop:t.offset().top-100},600)});var t=debounce((t,e)=>{a.submit()},400);a.find('input[type="text"]').on("input change",t),a.find('input[type="number"]').on("keyup",()=>a.submit()),a.find('input[type="hidden"]').on("change",()=>a.submit()),a.find('input[type="date"]').on("change",()=>a.submit()),a.find('input[type="checkbox"], select').on("change",()=>a.submit()),a.find('input[type="radio"], select').on("change",()=>a.submit()),a.on("submit",function(t){t.preventDefault();var t=a.attr("action"),e=a.serialize(),n=a.serializeArray(),t=r(`<a href="${t}"></a>`),t=t[0].href,e=e.split("&").filter(t=>{var[t,e]=t.split("=");return""!==e||"s"===t}),e=e.join("&"),t=t.replace(/\/page\/\d+/gi,""),e=e?t.split("?")[0]+"?"+e:t.split("?")[0];history.pushState({submitUrl:e,paramsArray:n,urlToLoad:e},"",e),i(e)}),window.onpopstate=function(l){let t="./";l.state&&(t=l.state.urlToLoad,a.find('input[type="checkbox"]').each(function(i){return function(){var t=r(this);const e=t.attr("name"),n=t.val();var a=i.some(t=>t.name===e&&t.value===n);t.prop("checked",a)}}(l.state.paramsArray)),a.find('input[type="hidden"]').each(function(t){var e=r(this);const n=e.attr("name");var a=e.val(),i=l.state.paramsArray.find(t=>t.name===n);let o=!1;i||(o=!0,e.val("")),i&&i.value!==a&&(o=!0,e.val(i.value)),o&&e.trigger("change")}),l.state.paramsArray.forEach(function(t){var e=a.find(`input[name="${t.name}"]`);["checkbox","radio"].includes(e.attr("type"))&&a.find(`input[name="${t.name}"][value="${t.value}"]`).prop("checked")}));i(t)}}))}(jQuery),function(e){var t=e(".wp-block-group.is-style-carousel");t.length&&t.each(function(){var t=e(this);t.slick({slidesToShow:1,slidesToScroll:1,autoplay:!1,dots:!1,arrows:!0,infinite:!1,autoplaySpeed:5e3,appendArrows:t,appendDots:t,prevArrow:'<button class="slick-prev"><span class="sr-only">prev</span><svg xmlns="http://www.w3.org/2000/svg" width="40" height="41" fill="none"><circle cx="20" cy="20.19" r="20" fill="currentColor"/><path fill="#fff" d="M26.791 19.19h-11.17l4.88-4.88c.39-.39.39-1.03 0-1.42a.996.996 0 0 0-1.41 0l-6.59 6.59a.996.996 0 0 0 0 1.41l6.58 6.6a.996.996 0 1 0 1.41-1.41l-4.87-4.89h11.17c.55 0 1-.45 1-1s-.45-1-1-1Z"/></svg></button>',nextArrow:'<button class="slick-next"><span class="sr-only">next</span><svg xmlns="http://www.w3.org/2000/svg" width="40" height="41" fill="none"><circle cx="20" cy="20.19" r="20" fill="currentColor"/><path fill="#fff" d="M13.209 21.19h11.17l-4.88 4.88c-.39.39-.39 1.03 0 1.42.39.39 1.02.39 1.41 0l6.59-6.59a.996.996 0 0 0 0-1.41l-6.58-6.6a.996.996 0 1 0-1.41 1.41l4.87 4.89h-11.17c-.55 0-1 .45-1 1s.45 1 1 1Z"/></svg></button>',responsive:[{breakpoint:768,settings:{slidesToShow:1}}]})})}(jQuery),function(a){function e(){const e=a(this);var t=e.attr("data-checkboxes");if(t){const n=a(`input[name="${t}"]`);n.on("change",function(t){t.preventDefault(),i(n,e)}),i(n,e)}}function i(t,e){t=t.filter(":checked").length;0===t?e.text(""):e.text(` (${t} selected)`)}a(document).ready(function(){var t=a(".js-selected-counter");t.length&&t.each(e)})}(jQuery),function(o){const t=o(".wp-block-columns.is-style-reverse-order");let l,e=!1;function n(){e||(e=!0,t.each(function(){var t,e,n=o(this),a=function(t){let e=!1,n,a,i=!1;return a=t.data("columns").first()[0].getBoundingClientRect().top,t.data("columns").each(function(){(n=this.getBoundingClientRect().top)!==a&&(i=!0)}),e=i?!0:e}(n),i=n.data("collapsed");a&&!i?(n.data("collapsed",!0),(e=n).data("columns").each(function(){(l=o(this)).prependTo(e)})):!a&&i&&(n.data("collapsed",!1),(t=n).data("columns").sort(function(t,e){return o(t).data("original-order")-o(e).data("original-order")}),t.data("columns").each(function(){(l=o(this)).appendTo(t)}))}),e=!1)}t.each(function(){var t=o(this);t.data("columns",t.find("> .wp-block-column")),t.data("collapsed",!1),t.data("columns").each(function(t){o(this).data("original-order",t)})}),n();let a;o(window).on("resize",function(){clearTimeout(a),a=setTimeout(n,20)})}(jQuery),function(o){if("undefined"!=typeof CountUp){let a=null,i=-1;const s=Math.max(document.documentElement.clientHeight,window.innerHeight);var t;function e(){var t=o(this),e=t.html().replace(/,/gi,"");t.css({minWidth:t.width()+10+"px",textAlign:"right"}),t.html(0),t.attr("data-count-start",0),t.attr("data-count-end",e)}function n(){var t=o(this),e=t.text().replace(/[\d,\,,\.]+/g,'<span class="stat-value-countup">$&</span>');t.html(e)}function l(t){var e=t.data("count-end"),n=t.data("count-start")||parseFloat(t.text());t.data("count-start",e);let a=0;if(e%1&&(a=e.toString().split(".")[1].length||0),n!==e){new CountUp(t[0],n,e,a,.6,{useEasing:!0}).start();t.data("statHighlights").each(function(t){setTimeout(()=>{o(this).removeClass("force-unhighlight")},600+800*t)})}}function r(){(a=o('[data-count-end]:not(".counted")')).each(function(){var t=o(this);t.data("statHighlights",t.closest(".wp-block-group").find(".guidance-report-statistic strong")),t.data("statHighlights").length&&t.data("statHighlights").addClass("force-unhighlight")})}(t=o(".count-up")).length&&(t.each(n),(t=o(".stat-value-countup")).length&&t.each(e)),r(),setTimeout(function n(){if(i===window.pageYOffset)window.requestAnimationFrame(n);else{i=window.pageYOffset;let t=null,e=!1;a.each(function(){t=o(this),function(t,e=200){return t=(t=t.getBoundingClientRect()).top+e<s}(this)&&!t.hasClass("counted")&&(t.addClass("counted"),e=!0,l(t))}),e&&r(),a.length&&window.requestAnimationFrame(n)}},1e3),window.runCountUp=l}}(jQuery),function(o){const t=o(".reverse-order-on-mob");let l,e=!1;function n(){e||(e=!0,t.each(function(){var t,e,n=o(this),a=function(t){let e=!1,n,a,i=!1;return a=t.data("columns").first()[0].getBoundingClientRect().top,t.data("columns").each(function(){(n=this.getBoundingClientRect().top)!==a&&(i=!0)}),e=i?!0:e}(n),i=n.data("collapsed");a&&!i?(n.data("collapsed",!0),(e=n).data("columns").each(function(){(l=o(this)).prependTo(e)})):!a&&i&&(n.data("collapsed",!1),(t=n).data("columns").sort(function(t,e){return o(t).data("original-order")-o(e).data("original-order")}),t.data("columns").each(function(){(l=o(this)).appendTo(t)}))}),e=!1)}t.each(function(){var t=o(this);t.data("columns",t.find("> *")),t.data("collapsed",!1),t.data("columns").each(function(t){o(this).data("original-order",t)})}),n();let a;o(window).on("resize",function(){clearTimeout(a),a=setTimeout(n,20)})}(jQuery),function(n){var t=n('[class*="js_pre_fill_"]');t.length&&t.each(function(){var t=n(this),e=t.attr("class").split("js_pre_fill_")[1].split(" ")[0],e=new URLSearchParams(window.location.search).get(e);e&&t.find("input").val(e)})}(jQuery),function(c){const d=c(".js-options-container");if(d.length){let s=window.innerWidth<968;window.addEventListener("resize",debounce(function(){s=window.innerWidth<968},100)),d.each(function(){const n=c(this),a="true"===n.attr("data-options-container-mobile-only"),t=n.attr("aria-labelledby");if(t){var e=c("#"+t);if(e.length){var i=n.attr("id")||"options-container-"+Math.floor(1e5*Math.random());n.attr("id",i);const r=c(`<button class="options-trigger" aria-expanded="true" controls="${i}">`).html(e.html()).insertBefore(e);function o(t){c(t.target).closest(".option-select-group").length||(t=d.filter('[aria-hidden="false"]')).length&&t.each((t,e)=>{c(e).slideUp(200).attr("aria-hidden",!0),c(e).data("button").attr("aria-expanded",!1)})}function l(t){var e;t.preventDefault(),(s&&a||!a)&&(t="true"===r.attr("aria-expanded"),r.attr("aria-expanded",!t),t?(n.slideUp(200),c("body").off("click",o)):(c("body").on("click",o),n.slideDown(300),n.is("[one-open]")&&(e=d.not(n)).length&&(e.slideUp(200).attr("aria-hidden",!0),e.data("button").attr("aria-expanded",!1))),n.attr("aria-hidden",t))}n.data("button",r),r.addClass(e.attr("class")).attr("id",e.attr("id")),e.remove(),r.on("click",l),n.find("input:checked").length&&(!n.is("[close-initial]")||n.is("[open-initial]"))||(n.hide(),l({preventDefault:()=>{}})),a&&(window.addEventListener("resize",debounce(()=>{s?r.show():(r.hide(),"true"!==r.attr("aria-expanded")&&(n.show(),n.attr("aria-hidden",!1)))},200)),window.dispatchEvent(new Event("resize")))}}})}}(jQuery),function(e){var t=document.querySelectorAll(".main-nav a"),n=e(".breadcrumbs").find("li:nth-child(2) a");const a=(n.length?n.attr("href"):window.location.href).split("?")[0];t.forEach(t=>{t.href.split("?")[0]===a&&((t=e(t)).addClass("current_page_item"),t.closest(".main-nav > li.menu-item").addClass("current_page_item"))})}(jQuery),function(t){const e=t("#toggle"),n=t(".navs-wrap"),a=t(".navs-wrap"),i=t(".main-header");e.on("click",function(){var t="true"===this.getAttribute("aria-expanded");this.setAttribute("aria-expanded",!t),t?(e.attr("aria-expanded",!1),i.removeClass("expanded"),n.removeClass("expanded"),bodyScrollLock.enableBodyScroll(a[0])):(e.attr("aria-expanded",!0),i.addClass("expanded"),n.addClass("expanded"),bodyScrollLock.disableBodyScroll(a[0]))})}(jQuery),jQuery(".social-sharing-link--print").on("click",function(t){t.preventDefault(),window.print()}),function(){var t=document.querySelectorAll(".js-clear");t&&t.forEach(i=>{const o=i.closest("form"),l=()=>{var t=o.querySelectorAll('input:not([type="radio"]):not([type="checkbox"]):not(.filter-long-list__search-field)'),e=o.querySelectorAll("input:checked"),n=o.querySelectorAll("textarea"),a=o.querySelectorAll("select:not(.js-ignore-clear)"),t=[...t,...e,...n,...a].map(t=>t.value).every(t=>""===t);i.classList.toggle("hidden-clear",t)};l(),o.querySelectorAll("input, select:not(.js-ignore-clear), textarea").forEach(t=>{t.addEventListener("input",l)}),i.addEventListener("click",t=>{t.preventDefault();var t=o.querySelectorAll('input:not([type="radio"]):not([type="checkbox"]):not(.filter-long-list__search-field)'),e=o.querySelectorAll("input:checked"),n=o.querySelectorAll("textarea"),a=o.querySelectorAll("select:not(.js-ignore-clear)");t.forEach(t=>{t.value=""}),e.forEach(t=>{t.checked=!1}),n.forEach(t=>{t.value=""}),a.forEach(t=>{t.value=""});const i=[];[...e,...a].forEach(t=>{-1===i.indexOf(t.name)&&(i.push(t.name),t.dispatchEvent(new Event("change")))}),[...t,...n].forEach(t=>{-1===i.indexOf(t.name)&&(i.push(t.name),t.dispatchEvent(new Event("input")))}),l()})})}(),/(trident|msie)/i.test(navigator.userAgent)&&document.getElementById&&window.addEventListener&&window.addEventListener("hashchange",function(){var t=location.hash.substring(1);/^[A-z0-9_-]+$/.test(t)&&(t=document.getElementById(t))&&(/^(?:a|select|input|button|textarea)$/i.test(t.tagName)||(t.tabIndex=-1),t.focus())},!1),function(i){function n(e){e.preventDefault();var n,e=i(this).data("id");const a=i(document.querySelector(`[id="${e}"]`));if(a.length){let t=a.offset().top;(a.hasClass("is-style-sticky")||a.closest(".is-style-sticky").length)&&(n=a.closest(".wp-block-columns, .wp-block-cover, .entry-content"),t=n.offset().top),i(".intervention-page__nav").length&&(t-=i(".intervention-page__nav").outerHeight()+24),history.replaceState(null,null,"#"+e),i("html, body").animate({scrollTop:t},300,"linear",()=>{a.attr("tabindex","-1").focus()})}}window.SET_SMOOTHSCROLLERS=function(t=null){let e=t;(e=e||i('a[href^="#"]:not(.js-smooth-scroll), a[href^="'+location.origin+location.pathname+'#"]:not(.js-smooth-scroll)')).length&&e.each(function(){var t=i(this),e=t.attr("href").split("#")[1];"#"!==e&&(t.data("id",e),t.addClass("js-smooth-scroll"),t.off("click",n),t.on("click",n))})},window.SET_SMOOTHSCROLLERS()}(jQuery),function(d){const t=d(window),s=d(".js-sub-nav"),c=s.find("a");let u=null,h=null;const p=c.map(function(){var t=d(this).attr("href").replace("#",""),t=document.getElementById(t);if(t)return d(t)});if(0!==p.length){let a=null,n=(document.addEventListener("DOMContentLoaded",function(){a=d('<div class="anchor-line" aria-hidden="true"></div>'),d("body").append(a),a.empty(),p.each(function(t,e){var n=d('<div class="anchor-line__section"></div>');n.data("target",d(e)),n.data("trigger",c[t]),a.append(n)}),h=a.find(".anchor-line__section"),e(),t.on("scroll",function(){n||i===window.scrollY||(i=window.scrollY,window.requestAnimationFrame(function(){{var t;const l=i+window.innerHeight/2,e=s[0].getBoundingClientRect(),r=e.left+e.width/2;let o=null;h.each(function(t){var e=d(this),n=e.position().top,a=e.height(),i=n+a,e=e.data("trigger"),a=Math.max(Math.min((l-n)/a,1),0);0<a&&(o=e),e.style.setProperty("--complete",a),i<l?e.classList.add("highlighted"):n<l&&i>l?u!==t&&(u=t,e.classList.remove("highlighted"),c.removeClass("current"),e.classList.add("current"),n=(a=e.getBoundingClientRect()).left+a.width/2,i=s[0].scrollLeft,t=n-r+i,s.animate({scrollLeft:t},400)):(e.classList.remove("selected"),e.classList.remove("highlighted"))}),o&&o.classList.add("selected")}n=!1})),n=!0}),t.on("resize",function(a,i,o){let l;return function(){let t=this,e=arguments;var n=o&&!l;clearTimeout(l),l=setTimeout(function(){l=null,o||a.apply(t,e)},i),n&&a.apply(t,e)}}(function(){e()},300))}),window.addEventListener("load",function(){this.setTimeout(e,300)}),!1),i=0;function e(){const a=window.scrollY;let i=null,o=null,l=null,r=null,s=null,c=!1;h.each(function(t){l=d(this),r=l.data("target"),s=null,c=!1,i=null,o=null,p[t+1]?s=d(p[t+1]):(s=r.closest(".wp-block-columns, .wp-block-cover, .entry-content"),c=!0),i=r.hasClass("is-style-sticky")?r:r.closest(".is-style-sticky"),o=s.hasClass("is-style-sticky")?s:s.closest(".is-style-sticky"),i.length&&i.removeClass("is-style-sticky"),o.length&&o.removeClass("is-style-sticky");var e=r[0].getBoundingClientRect().top+a,n=(c?s[0].getBoundingClientRect().bottom+a:s[0].getBoundingClientRect().top+a)-e;i.length&&i.addClass("is-style-sticky"),o.length&&o.addClass("is-style-sticky"),l.css({background:t%2==0?"red":"blue",top:e,height:n,left:t%2==0?"0":"10px"})})}}}(jQuery),function(o){var t=o(".tabs");t.length&&(t.each(function(){const n=o(this),a=n.find(".tabs__link"),i=n.find(".tabs__panel");a.on("click",function(t){t.preventDefault();var t=o(this),e=t.attr("href"),e=n.find(e);a.attr("aria-selected","false"),t.attr("aria-selected","true"),i.attr("aria-hidden","true"),e.attr("aria-hidden","false")})}),o(".js-fullscreen").on("click",function(t){t.preventDefault();t=o(this),t=o(t.attr("data-target"));t.length&&(t.hasClass("fullscreen-view")?(t.removeClass("fullscreen-view"),bodyScrollLock.enableBodyScroll(t[0])):(t.addClass("fullscreen-view"),bodyScrollLock.disableBodyScroll(t[0])))}))}(jQuery),function(a){if(window.TOOLTIP_URL){var t=a(`a[href^="${window.TOOLTIP_URL}"]:not(.tooltip)`);if(t.each(function(){var t=a(this),e=a("<button>",{class:"tooltip",html:t.html(),"data-href":t.attr("href")});t.replaceWith(e)}),t.length){let e=!1;t.on("touchstart",function(){e=!0}),t.on("click",function(t){!this._tippy.state.isShown&&e&&t.preventDefault()}),tippy(".tooltip",{touch:!0,trigger:"mouseenter click touchstart",interactive:!0,aria:{content:"describedby"},allowHTML:!0,onCreate(t){t._isFetching=!1,t.setContent("Loading...")},onShow(n){if(!n._isFetching){n._isFetching=!0;var t=a(n.reference).attr("data-href");let e="Could not find definition";a.get(t,function(t){t=a(t);t.length&&(e=t.html()),n.setContent(e)})}}})}}}(jQuery),window?.MATOMO_EVENTS&&window?._paq&&window.MATOMO_EVENTS.forEach(t=>{const{trigger_selector:e,event_type:n,event_category:a,event_action:i,event_value_type:o,event_value_attribute:l,event_value:r}=t;document.querySelectorAll(e).forEach(t=>{t.addEventListener(n,function(){let t=r;"attribute"===o?t="value"!==l?this.getAttribute(l):this.value:"innerText"===o&&(t=this.innerText),_paq.push(["trackEvent",a,i,t])})})});
//# sourceMappingURL=main.min.js.map
