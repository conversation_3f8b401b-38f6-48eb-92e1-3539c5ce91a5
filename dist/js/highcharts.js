!function r(o,i,s){function a(e,t){if(!i[e]){if(!o[e]){var n="function"==typeof require&&require;if(!t&&n)return n(e,!0);if(u)return u(e,!0);throw new Error("Cannot find module '"+e+"'")}t=i[e]={exports:{}};o[e][0].call(t.exports,function(t){var n=o[e][1][t];return a(n||t)},t,t.exports,r,o,i,s)}return i[e].exports}for(var u="function"==typeof require&&require,t=0;t<s.length;t++)a(s[t]);return a}({1:[function(t,n,c){!function(o){function i(t,n){for(var e=0,r=t.length-1;0<=r;r--){var o=t[r];"."===o?t.splice(r,1):".."===o?(t.splice(r,1),e++):e&&(t.splice(r,1),e--)}if(n)for(;e--;)t.unshift("..");return t}function s(t,n){if(t.filter)return t.filter(n);for(var e=[],r=0;r<t.length;r++)n(t[r],r,t)&&e.push(t[r]);return e}c.resolve=function(){for(var t="",n=!1,e=arguments.length-1;-1<=e&&!n;e--){var r=0<=e?arguments[e]:o.cwd();if("string"!=typeof r)throw new TypeError("Arguments to path.resolve must be strings");r&&(t=r+"/"+t,n="/"===r.charAt(0))}return(n?"/":"")+(t=i(s(t.split("/"),function(t){return!!t}),!n).join("/"))||"."},c.normalize=function(t){var n=c.isAbsolute(t),e="/"===r(t,-1);return(t=(t=i(s(t.split("/"),function(t){return!!t}),!n).join("/"))||n?t:".")&&e&&(t+="/"),(n?"/":"")+t},c.isAbsolute=function(t){return"/"===t.charAt(0)},c.join=function(){var t=Array.prototype.slice.call(arguments,0);return c.normalize(s(t,function(t,n){if("string"!=typeof t)throw new TypeError("Arguments to path.join must be strings");return t}).join("/"))},c.relative=function(t,n){function e(t){for(var n=0;n<t.length&&""===t[n];n++);for(var e=t.length-1;0<=e&&""===t[e];e--);return e<n?[]:t.slice(n,e-n+1)}t=c.resolve(t).substr(1),n=c.resolve(n).substr(1);for(var r=e(t.split("/")),o=e(n.split("/")),i=Math.min(r.length,o.length),s=i,a=0;a<i;a++)if(r[a]!==o[a]){s=a;break}for(var u=[],a=s;a<r.length;a++)u.push("..");return(u=u.concat(o.slice(s))).join("/")},c.sep="/",c.delimiter=":",c.dirname=function(t){if("string"!=typeof t&&(t+=""),0===t.length)return".";for(var n=47===t.charCodeAt(0),e=-1,r=!0,o=t.length-1;1<=o;--o)if(47===t.charCodeAt(o)){if(!r){e=o;break}}else r=!1;return-1===e?n?"/":".":n&&1===e?"/":t.slice(0,e)},c.basename=function(t,n){t=function(t){"string"!=typeof t&&(t+="");for(var n=0,e=-1,r=!0,o=t.length-1;0<=o;--o)if(47===t.charCodeAt(o)){if(!r){n=o+1;break}}else-1===e&&(r=!1,e=o+1);return-1===e?"":t.slice(n,e)}(t);return t=n&&t.substr(-1*n.length)===n?t.substr(0,t.length-n.length):t},c.extname=function(t){"string"!=typeof t&&(t+="");for(var n=-1,e=0,r=-1,o=!0,i=0,s=t.length-1;0<=s;--s){var a=t.charCodeAt(s);if(47===a){if(o)continue;e=s+1;break}-1===r&&(o=!1,r=s+1),46===a?-1===n?n=s:1!==i&&(i=1):-1!==n&&(i=-1)}return-1===n||-1===r||0===i||1===i&&n===r-1&&n===e+1?"":t.slice(n,r)};var r="b"==="ab".substr(-1)?function(t,n,e){return t.substr(n,e)}:function(t,n,e){return n<0&&(n=t.length+n),t.substr(n,e)}}.call(this,t("3kpn51"))},{"3kpn51":2}],2:[function(t,n,e){var r,o,i,n=n.exports={};function s(){}n.nextTick=(o="undefined"!=typeof window&&window.setImmediate,i="undefined"!=typeof window&&window.postMessage&&window.addEventListener,o?function(t){return window.setImmediate(t)}:i?(r=[],window.addEventListener("message",function(t){var n=t.source;n!==window&&null!==n||"process-tick"!==t.data||(t.stopPropagation(),0<r.length&&r.shift()())},!0),function(t){r.push(t),window.postMessage("process-tick","*")}):function(t){setTimeout(t,0)}),n.title="browser",n.browser=!0,n.env={},n.argv=[],n.on=s,n.addListener=s,n.once=s,n.off=s,n.removeListener=s,n.removeAllListeners=s,n.emit=s,n.binding=function(t){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(t){throw new Error("process.chdir is not supported")}},{}],3:[function(t,n,e){"use strict";t("path").format;jQuery,t=Highcharts.getOptions().colors.map(function(t,n){return Highcharts.color("#8F2B65").brighten(n/10).get()}),Highcharts.setOptions({colors:t,responsive:{rules:[{condition:{minWidth:1},chartOptions:{plotOptions:{bar:{tooltip:{pointFormatter:function(){var t=this.series.tooltipOptions.valuePrefix||"",n=this.series.tooltipOptions.valueSuffix||"",e=this.y;return"".concat(t).concat(e).concat(n)}}}}}},{condition:{maxWidth:700},chartOptions:{plotOptions:{pie:{label:{formatter:function(){return this.y}},dataLabels:{enabled:!0,distance:1,style:{fontSize:"8px"},format:"{point.name}"},showInLegend:!0}},legend:{enabled:!0,align:"center",labelFormatter:function(){return"".concat(this.name,": <em>").concat(Math.round(this.percentage),"%</em>")},verticalAlign:"bottom",layout:"horizontal"}}},{condition:{maxWidth:500},chartOptions:{plotOptions:{pie:{dataLabels:{enabled:!1}}},legend:{enabled:!0,align:"center",labelFormatter:function(){return"".concat(this.name,": <em>").concat(Math.round(this.percentage),"%</em>")},verticalAlign:"bottom",layout:"horizontal"}}}]}})},{path:1}]},{},[3]);