!function(e,o){"function"==typeof define&&define.amd?define(["exports"],o):"undefined"!=typeof exports?o(exports):(o(o={}),e.bodyScrollLock=o)}(this,function(e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o,t=!1;function r(o){return d.some(function(e){return!(!e.options.allowTouchMove||!e.options.allowTouchMove(o))})}function i(e){e=e||window.event;return!!r(e.target)||1<e.touches.length||(e.preventDefault&&e.preventDefault(),!1)}function n(){void 0!==s&&(document.body.style.paddingRight=s,s=void 0),void 0!==u&&(document.body.style.overflow=u,u=void 0)}"undefined"!=typeof window&&(window.addEventListener("testPassive",null,o={get passive(){t=!0}}),window.removeEventListener("testPassive",null,o));var l="undefined"!=typeof window&&window.navigator&&window.navigator.platform&&(/iP(ad|hone|od)/.test(window.navigator.platform)||"MacIntel"===window.navigator.platform&&1<window.navigator.maxTouchPoints),d=[],c=!1,a=-1,u=void 0,s=void 0;e.disableBodyScroll=function(n,e){var o;n?d.some(function(e){return e.targetElement===n})||(o={targetElement:n,options:e||{}},d=[].concat(function(e){if(Array.isArray(e)){for(var o=0,t=Array(e.length);o<e.length;o++)t[o]=e[o];return t}return Array.from(e)}(d),[o]),l?(n.ontouchstart=function(e){1===e.targetTouches.length&&(a=e.targetTouches[0].clientY)},n.ontouchmove=function(e){var o,t;1===e.targetTouches.length&&(o=n,t=(e=e).targetTouches[0].clientY-a,r(e.target)||(o&&0===o.scrollTop&&0<t||o&&o.scrollHeight-o.scrollTop<=o.clientHeight&&t<0?i(e):e.stopPropagation()))},c||(document.addEventListener("touchmove",i,t?{passive:!1}:void 0),c=!0)):(void 0===s&&(o=!!e&&!0===e.reserveScrollBarGap,e=window.innerWidth-document.documentElement.clientWidth,o&&0<e&&(s=document.body.style.paddingRight,document.body.style.paddingRight=e+"px")),void 0===u&&(u=document.body.style.overflow,document.body.style.overflow="hidden"))):console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.")},e.clearAllBodyScrollLocks=function(){l?(d.forEach(function(e){e.targetElement.ontouchstart=null,e.targetElement.ontouchmove=null}),c&&(document.removeEventListener("touchmove",i,t?{passive:!1}:void 0),c=!1),a=-1):n(),d=[]},e.enableBodyScroll=function(o){o?(d=d.filter(function(e){return e.targetElement!==o}),l?(o.ontouchstart=null,o.ontouchmove=null,c&&0===d.length&&(document.removeEventListener("touchmove",i,t?{passive:!1}:void 0),c=!1)):d.length||n()):console.error("enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.")}});