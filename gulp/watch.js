const gulp = require('gulp');
const paths = gulp.paths;
// const browserSync = require('browser-sync').create();

// gulp.task('browser-sync', function () {
// 	browserSync.init({
// 		proxy: 'dev.localhost.com', // Change to your local dev URL
// 		notify: false,
// 		open: false,
// 	});
// });

gulp.task('watch:css', function () {
	return gulp.watch(
		[paths.src + '/styles/**/*.sass', 'components/**/*.sass', 'blocks/**/*.sass'],
		gulp.series('css', function (done) {
			// browserSync.reload();
			done();
		}),
	);
});

gulp.task('watch:css-sep', function () {
	return gulp.watch(
		[paths.src + '/styles/separate/*.sass'],
		// gulp.parallel('css')
		gulp.series('css-separate'),
	);
});

gulp.task('watch:images', function () {
	return gulp.watch(paths.images.watch, gulp.parallel('image-compression'));
});

gulp.task('watch:svg', function () {
	return gulp.watch(paths.images.svgWatch, gulp.parallel('svg-compression'));
});

gulp.task('watch:js', function () {
	return gulp.watch(paths.js.src, gulp.parallel('js-scripts'));
});

gulp.task('watch:js-vendor', function () {
	return gulp.watch(paths.js.vendor, gulp.parallel('js-vendor'));
});

gulp.task('watch:js-separate', function () {
	return gulp.watch(paths.js.separate, gulp.parallel('js-separate'));
});

gulp.task(
	'watch',
	gulp.parallel(
		// 'browser-sync',
		'watch:css',
		'watch:css-sep',
		'watch:images',
		'watch:svg',
		'watch:js',
		'watch:js-vendor',
		'watch:js-separate',
	),
);
