const gulp = require('gulp');
const paths = gulp.paths;
const cssnano = require('cssnano');
const sass = require('gulp-sass')(require('sass'));

const $ = require('gulp-load-plugins')();

gulp.task('css', function () {
	const postcss = require('gulp-postcss');

	return gulp
		.src([paths.src + '/styles/main.sass'])
		.pipe(
			sass({
				// Add these options to suppress the deprecation warnings
				quietDeps: true, // Silences warnings from dependencies
				verbose: false, // Reduces the number of deprecation warnings
				logger: {
					// info: () => {},
					warn: () => {},
					// error: () => {},
					// debug: () => {},
				},
			}),
		)
		.pipe(gulp.dest(paths.dist + '/css/.tmp.css'))
		.on('error', function (err) {
			console.error('SASS error: ', err.message);
		})
		.pipe(postcss([require('autoprefixer')()]))
		.on('error', function (err) {
			console.error('Error with prefix!', err.message);
		})
		.pipe(
			postcss([
				cssnano({
					preset: 'default',
				}),
			]),
		)
		.on('error', function (err) {
			console.error('Error with nano!', err.message);
		})
		.pipe(
			$.purgecss({
				content: ['./template-parts/*.php', './components/**/*.php', './inc/**/*.php', './*.php'],
				skippedContentGlobs: ['node_modules/**'],
				safelist: {
					standard: [/is-/, /has-/, /active/, /wp-/],
					deep: [/modal/, /dropdown/],
					greedy: [/:is/, /:where/],
				},
			}),
		)
		.pipe($.sourcemaps.write('.'))
		.pipe(gulp.dest(paths.dist + '/css'))
		.pipe($.notify({ message: 'Main SASS processed successfully' }));
});

gulp.task('css-separate', function () {
	const postcss = require('gulp-postcss');

	return gulp
		.src([paths.src + '/styles/separate/*.sass'])
		.pipe(sass())
		.pipe(gulp.dest(paths.dist + '/css/.sep.css'))
		.on('error', function (err) {
			console.error('SASS error: ', err.message);
		})
		.pipe(postcss([require('autoprefixer')()]))
		.on('error', function (err) {
			console.error('Error with Separate prefix!', err.message);
		})
		.pipe(
			postcss([
				cssnano({
					preset: 'default',
				}),
			]),
		)
		.on('error', function (err) {
			console.error('Error with nano!', err.message);
		})
		.pipe(gulp.dest(paths.dist + '/css'))
		.pipe($.notify({ message: 'Separate SASS processed successfully' }));
});

gulp.task('admin-css', function () {
	const postcss = require('gulp-postcss');

	return gulp
		.src([paths.src + '/styles/admin.sass'])
		.pipe(
			sass({
				// Add these options to suppress the deprecation warnings
				quietDeps: true, // Silences warnings from dependencies
				verbose: false, // Reduces the number of deprecation warnings
			}),
		)
		.pipe(gulp.dest(paths.dist + '/css/.admin.tmp.css'))
		.on('error', function (err) {
			console.error('SASS error: ', err.message);
		})
		.pipe(postcss([require('autoprefixer')()]))
		.on('error', function (err) {
			console.error('Error with admin prefix!', err.message);
		})
		.pipe(
			postcss([
				cssnano({
					preset: 'default',
				}),
			]),
		)
		.on('error', function (err) {
			console.error('Error with admin nano!', err.message);
		})
		.pipe(gulp.dest(paths.dist + '/css'))
		.pipe($.notify({ message: 'Admin SASS processed successfully' }));
});
