const gulp = require('gulp');
const paths = gulp.paths;

const $ = require('gulp-load-plugins')();

function handleError(err) {
	console.log(err.toString());
	this.emit('end');
}

gulp.task('js-scripts', function () {
	return gulp
		.src(paths.js.src)
		.pipe($.sourcemaps.init())
		.pipe(
			$.babel({
				presets: [
					[
						'@babel/preset-env',
						{
							targets: {
								browsers: ['last 2 versions', 'not ie <= 11', 'not op_mini all', 'not dead'],
							},
							useBuiltIns: 'usage',
							corejs: 3,
							modules: false,
						},
					],
				],
				plugins: ['@babel/plugin-syntax-dynamic-import'],
			}),
		)
		.on('error', handleError)
		.pipe($.concat('main.min.js'))
		.pipe($.uglify({ compress: true }))
		.on('error', handleError)
		.pipe($.sourcemaps.write('.'))
		.pipe(gulp.dest(paths.js.dest));
});

gulp.task('js-scroll', function () {
	console.log(paths.scrollJs.src);
	return gulp
		.src(paths.scrollJs.src)
		.pipe(
			$.babel({
				presets: ['@babel/preset-env'],
			}),
		)
		.pipe($.concat('scroll.js'))
		.pipe($.uglify({ compress: true }))
		.pipe(gulp.dest(paths.js.dest));
});

gulp.task('js-vendor', function () {
	return gulp
		.src(paths.js.vendor)
		.pipe($.concat('vendor.js'))
		.pipe($.uglify({ compress: true }))
		.pipe(gulp.dest(paths.js.dest));
});

gulp.task('js-separate', function () {
	return gulp
		.src(paths.js.separate)
		.pipe($.sourcemaps.init())
		.pipe(
			$.babel({
				presets: ['@babel/preset-env'],
			}),
		)
		.on('error', function handleError(err) {
			console.log('babel error', err);
			this.emit('end');
		})
		.pipe($.browserify())
		.on('error', function handleError(err) {
			console.log('browserify error', err);
			this.emit('end');
		})
		.pipe($.uglify({ compress: true }))
		.on('error', function handleError(err) {
			console.log('uglify error', err);
			this.emit('end');
		})
		.pipe(gulp.dest(paths.js.dest));
});
