const gulp = require('gulp');
const paths = gulp.paths;

const $ = require('gulp-load-plugins')();

gulp.task('image-compression', function () {
	return gulp
		.src(`${paths.images.src}*.{png,jpg,jpeg}`, { since: gulp.lastRun('image-compression') })
		.pipe(
			$.tinypngCompress({
				key: 'COtWntdDOKqvg6vRI4G9pgvRmJ5W5FXJ',
				sigFile: './src/images/.tinypng-sigs',
				log: true,
			}),
		)
		.pipe(gulp.dest(paths.images.dest));
});

gulp.task('svg-compression', function () {
	return gulp
		.src(`${paths.images.src}*.svg`)
		.pipe(
			$.svgo({
				plugins: [
					{
						inlineStyles: true,
					},
					{
						cleanupAttrs: true,
					},
					{
						removeDoctype: true,
					},
					{
						removeXMLProcInst: true,
					},
					{
						removeComments: true,
					},
					{
						removeMetadata: true,
					},
					{
						removeTitle: true,
					},
					{
						removeDesc: true,
					},
					{
						removeUselessDefs: true,
					},
					{
						removeEditorsNSData: true,
					},
					{
						removeEmptyAttrs: true,
					},
					{
						removeHiddenElems: true,
					},
					{
						removeEmptyText: true,
					},
					{
						removeEmptyContainers: true,
					},
					{
						cleanupEnableBackground: true,
					},
					{
						convertStyleToAttrs: true,
					},
					{
						convertColors: true,
					},
					{
						convertPathData: true,
					},
					{
						convertTransform: true,
					},
					{
						removeUnknownsAndDefaults: true,
					},
					{
						removeNonInheritableGroupAttrs: true,
					},
					{
						removeUselessStrokeAndFill: true,
					},
					{
						removeUnusedNS: true,
					},
					{
						cleanupIDs: true,
					},
					{
						cleanupNumericValues: true,
					},
					{
						moveElemsAttrsToGroup: true,
					},
					{
						moveGroupAttrsToElems: true,
					},
					{
						collapseGroups: true,
					},
					{
						removeRasterImages: false,
					},
					{
						mergePaths: true,
					},
					{
						convertShapeToPath: true,
					},
					{
						sortAttrs: true,
					},
					{
						removeDimensions: true,
					},
				],
			}),
		)
		.pipe(gulp.dest(paths.images.dest));
});
