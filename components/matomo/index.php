<?php
if (function_exists('acf_add_options_page')) {
	acf_add_options_sub_page(array(
		'page_title'    => 'Matomo Event Tracking',
		'menu_title'    => 'Matomo Events',
		'parent_slug'   => 'options-general.php',
		// 'capability'    => 'activate_plugins',
		'capability'    => 'activate_plugins',
		'redirect'      => false,
	));
	acf_add_local_field_group(array(
		'key' => 'group_63c69495915d5',
		'title' => 'Matomo events',
		'fields' => array(
			array(
				'key' => 'field_63c69495e0df6',
				'label' => 'Matomo Event Tracking',
				'name' => 'matomo_event_tracking',
				'aria-label' => '',
				'type' => 'repeater',
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'layout' => 'table',
				'pagination' => 0,
				'min' => 0,
				'max' => 0,
				'collapsed' => '',
				'button_label' => 'Add event',
				'rows_per_page' => 20,
				'sub_fields' => array(
					array(
						'key' => 'field_63c694ace0df7',
						'label' => 'Enabled',
						'name' => 'enabled',
						'aria-label' => '',
						'type' => 'true_false',
						'instructions' => '',
						'required' => 0,
						'conditional_logic' => 0,
						'wrapper' => array(
							'width' => '',
							'class' => '',
							'id' => '',
						),
						'message' => '',
						'default_value' => 0,
						'ui_on_text' => 'Yes',
						'ui_off_text' => 'No',
						'ui' => 1,
						'parent_repeater' => 'field_63c69495e0df6',
					),
					array(
						'key' => 'field_63c69a9669a3f',
						'label' => 'Trigger selector',
						'name' => 'trigger_selector',
						'aria-label' => '',
						'type' => 'text',
						'instructions' => '',
						'required' => 1,
						'conditional_logic' => 0,
						'wrapper' => array(
							'width' => '',
							'class' => '',
							'id' => '',
						),
						'default_value' => '',
						'maxlength' => '',
						'placeholder' => 'a.className[href$="google.com"]',
						'prepend' => '',
						'append' => '',
						'parent_repeater' => 'field_63c69495e0df6',
					),
					array(
						'key' => 'field_63c694cce0df8',
						'label' => 'Event type',
						'name' => 'event_type',
						'aria-label' => '',
						'type' => 'select',
						'instructions' => '',
						'required' => 0,
						'conditional_logic' => 0,
						'wrapper' => array(
							'width' => '',
							'class' => '',
							'id' => '',
						),
						'choices' => array(
							'click' => 'Click',
							'change' => 'Change',
							'submit' => 'Submit',
						),
						'default_value' => 'click',
						'return_format' => 'value',
						'multiple' => 0,
						'allow_null' => 0,
						'ui' => 0,
						'ajax' => 0,
						'placeholder' => '',
						'parent_repeater' => 'field_63c69495e0df6',
					),
					array(
						'key' => 'field_63c694f2e0df9',
						'label' => 'Event category',
						'name' => 'event_category',
						'aria-label' => '',
						'type' => 'text',
						'instructions' => '',
						'required' => 1,
						'conditional_logic' => 0,
						'wrapper' => array(
							'width' => '',
							'class' => '',
							'id' => '',
						),
						'default_value' => '',
						'maxlength' => '',
						'placeholder' => '',
						'prepend' => '',
						'append' => '',
						'parent_repeater' => 'field_63c69495e0df6',
					),
					array(
						'key' => 'field_63c6959ce0dfb',
						'label' => 'Event action',
						'name' => 'event_action',
						'aria-label' => '',
						'type' => 'text',
						'instructions' => '',
						'required' => 1,
						'conditional_logic' => 0,
						'wrapper' => array(
							'width' => '',
							'class' => '',
							'id' => '',
						),
						'default_value' => '',
						'maxlength' => '',
						'placeholder' => '',
						'prepend' => '',
						'append' => '',
						'parent_repeater' => 'field_63c69495e0df6',
					),
					array(
						'key' => 'field_63c695a7e0dfc',
						'label' => 'Event value type',
						'name' => 'event_value_type',
						'aria-label' => '',
						'type' => 'select',
						'instructions' => '',
						'required' => 1,
						'conditional_logic' => 0,
						'wrapper' => array(
							'width' => '',
							'class' => '',
							'id' => '',
						),
						'choices' => array(
							'fixed' => 'Fixed',
							'attribute' => 'Attribute',
							'innerText' => 'Inner text',
						),
						'default_value' => 'attribute',
						'return_format' => 'value',
						'multiple' => 0,
						'allow_null' => 0,
						'ui' => 0,
						'ajax' => 0,
						'placeholder' => '',
						'parent_repeater' => 'field_63c69495e0df6',
					),
					array(
						'key' => 'field_63c6999b172e6',
						'label' => 'Event value attribute',
						'name' => 'event_value_attribute',
						'aria-label' => '',
						'type' => 'select',
						'instructions' => '',
						'required' => 0,
						'conditional_logic' => array(
							array(
								array(
									'field' => 'field_63c695a7e0dfc',
									'operator' => '==',
									'value' => 'attribute',
								),
							),
						),
						'wrapper' => array(
							'width' => '',
							'class' => '',
							'id' => '',
						),
						'choices' => array(
							'href' => 'HREF',
							'value' => 'Value',
							'data' => 'Data field',
						),
						'default_value' => false,
						'return_format' => 'value',
						'multiple' => 0,
						'allow_null' => 0,
						'ui' => 0,
						'ajax' => 0,
						'placeholder' => '',
						'parent_repeater' => 'field_63c69495e0df6',
					),
					array(
						'key' => 'field_63c699e6172e7',
						'label' => 'Event value',
						'name' => 'event_value',
						'aria-label' => '',
						'type' => 'text',
						'instructions' => '',
						'required' => 0,
						'conditional_logic' => array(
							array(
								array(
									'field' => 'field_63c695a7e0dfc',
									'operator' => '==',
									'value' => 'fixed',
								),
							),
						),
						'wrapper' => array(
							'width' => '',
							'class' => '',
							'id' => '',
						),
						'default_value' => '',
						'maxlength' => '',
						'placeholder' => '',
						'prepend' => '',
						'append' => '',
						'parent_repeater' => 'field_63c69495e0df6',
					),
				),
			),
		),
		'location' => array(
			array(
				array(
					'param' => 'options_page',
					'operator' => '==',
					'value' => 'acf-options-matomo-events',
				),
			),
		),
		'menu_order' => 0,
		'position' => 'normal',
		'style' => 'default',
		'label_placement' => 'top',
		'instruction_placement' => 'label',
		'hide_on_screen' => '',
		'active' => true,
		'description' => '',
		'show_in_rest' => 0,
	));
}


// if the options page has any fields, embed a js
function queue_events_script()
{
	if (!function_exists('get_field')) {
		return;
	}

	// TODO: rename this to matomo_events
	$fields = get_field('matomo_event_tracking', 'option');


	if (!$fields) {
		return;
	}

	// filter out fields where enabled is false
	$fields = array_filter($fields, function ($field) {
		return $field['enabled'];
	});

	// reset array keys so it's an array and not an object
	$fields = array_values($fields);

	if ($fields) {
		wp_enqueue_script('dias_matomo_events', get_template_directory_uri() . '/components/matomo/script.js', array(), STATIC_VERSION, true);
		// enqueue inline script
		wp_add_inline_script('dias_matomo_events', 'window.MATOMO_EVENTS = ' . json_encode($fields) . ';', 'before');
	}
}

add_action('wp_enqueue_scripts', 'queue_events_script');
