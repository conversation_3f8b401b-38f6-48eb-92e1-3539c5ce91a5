(function () {
	if (!window?.MATOMO_EVENTS || !window?._paq) {
		return;
	}

	window.MATOMO_EVENTS.forEach((event) => {
		const {
			trigger_selector,
			event_type,
			event_category,
			event_action,
			event_value_type,
			event_value_attribute,
			event_value,
		} = event;

		document.querySelectorAll(trigger_selector).forEach((element) => {
			element.addEventListener(event_type, function () {
				let val = event_value;
				if (event_value_type === 'attribute') {
					val =
						event_value_attribute !== 'value'
							? this.getAttribute(event_value_attribute)
							: this.value;
				} else if (event_value_type === 'innerText') {
					val = this.innerText;
				}

				_paq.push(['trackEvent', event_category, event_action, val]);
			});
		});
	});
})();
