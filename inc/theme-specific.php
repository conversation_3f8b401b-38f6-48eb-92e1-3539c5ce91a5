<?php
add_action('wp_footer', function () {
  if (wp_script_is('wdt-highcharts', 'enqueued')) {
    wp_enqueue_script(
      'dias_theme-highcharts-js',
      get_template_directory_uri() . '/dist/js/highcharts.js',
      array('wdt-highcharts'),
      STATIC_VERSION,
      true
    );
  }
}, 1);

/**
 * Add a custom link to the end of a specific menu that uses the wp_nav_menu() function
 */
add_filter('wp_nav_menu_items', 'add_admin_link', 10, 2);

function add_admin_link($items, $args)
{
  ob_start();
  if ($args->theme_location == 'top-menu') {
?>
    <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children header__search">
      <form action="<?= site_url(); ?>" class="header-search">
        <label for="header-search" class="sr-only">Search <?php bloginfo('name') ?></label>
        <input type="text" id="header-search" class="header-search__input" placeholder="Search <?php echo esc_attr(get_bloginfo('name')) ?>" name="s">

        <button class="header-search__submit" type="submit">
          <span class="sr-only">Search</span>
          <svg width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
            <path d="M8.5 0C5.5 0 3 2.5 3 5.5C3 6.6 3.3 7.6 3.9 8.5L0.5 11.5L1.5 12.6L4.9 9.7C5.9 10.6 7.1 11.1 8.5 11.1C11.5 11.1 14 8.6 14 5.6C14 2.5 11.5 0 8.5 0ZM8.5 9.5C6.3 9.5 4.5 7.7 4.5 5.5C4.5 3.3 6.3 1.5 8.5 1.5C10.7 1.5 12.5 3.3 12.5 5.5C12.5 7.7 10.7 9.5 8.5 9.5Z" fill="white" />
          </svg>
        </button>
      </form>
    </li>
<?php
  }

  $item = ob_get_clean();
  $items = $items . $item;
  return $items;
}
