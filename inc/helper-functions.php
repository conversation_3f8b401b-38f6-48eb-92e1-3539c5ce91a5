<?php

/**
 * Helper Functions
 *
 * @deprecated Use DiasTheme\Helpers class instead
 */

// Backward compatibility functions that map to the OOP methods

function dias_block_get_post_id()
{
	return DiasTheme\Helpers::getPostId();
}

function get_archive_post_type()
{
	return DiasTheme\Helpers::getArchivePostType();
}

function get_archive_page($post_type = false)
{
	return DiasTheme\Helpers::getArchivePage($post_type);
}

function get_archive_page_content($post_type = false)
{
	return DiasTheme\Helpers::getArchivePageContent($post_type);
}

function getPattern($title = 'Blog post hero')
{
	return DiasTheme\Helpers::getPattern($title);
}

function dias_get_users($types = [], $limit = 3, $order = 'order', $order_direction = 'ASC', $image_size = 320)
{
	return DiasTheme\Helpers::getUsers($types, $limit, $order, $order_direction, $image_size);
}

function get_author_data($author = 1, $imageSize = 320, $imageClass = '')
{
	return DiasTheme\Helpers::getAuthorData($author, $imageSize, $imageClass);
}

function get_co_authors_posts($author, $type = 'post')
{
	return DiasTheme\Helpers::getCoAuthorsPosts($author, $type);
}

function get_co_authors($post_id = false, $imageSize = 320, $imageClass = '')
{
	return DiasTheme\Helpers::getCoAuthors($post_id, $imageSize, $imageClass);
}

function dias_get_avatar($id_or_email, $size = 96, $default_value = '', $alt = '', $args = null)
{
	return DiasTheme\Helpers::getAvatar($id_or_email, $size, $default_value, $alt, $args);
}

function post_type_single_name($post_type = false)
{
	return DiasTheme\Helpers::getPostTypeSingleName($post_type);
}

function get_post_type_taxonomy($type)
{
	return DiasTheme\Helpers::getPostTypeTaxonomy($type);
}

function build_thumb_src_set($id = false, $options = false)
{
	return DiasTheme\Helpers::buildThumbSrcSet($id, $options);
}

function dias_highlight($string, $needle = false)
{
	return DiasTheme\Helpers::highlight($string, $needle);
}

function get_featured_ids_from_content($content = '')
{
	return DiasTheme\Helpers::getFeaturedIdsFromContent($content);
}

function is_get($key, $value, $echo = false)
{
	return DiasTheme\Helpers::isGet($key, $value, $echo);
}

function is_get_this($param, $value, $echo = false)
{
	$is_it = isset($_GET[$param]) && $_GET[$param] === $value;

	if ($echo) {
		echo $is_it ? $echo : '';
	}

	return $is_it;
}

function getPageAnchors($page_id)
{
	// get the contents of the page
	$content = get_post_field('post_content', $page_id);

	// get all elements with an ID attribute but make sure it doesn't get tabid
	$pattern = '/<[^>]+\ id="([^"]+)"/';

	// actually update the pattern so that it also ignores elements with the following classnames: c-accordion__title, c-accordion__content
	// $pattern = '/<[^>]+\ id="([^"]+)"[^>]*>(?:(?!c-accordion__title|c-accordion__content).)*<\/[^>]+>/';
	$pattern =
		'/<[^>]+id="([^"]+)"(?![^>]*class="[^"]*(c-accordion__title|c-accordion__content)[^"]*")[^>]*>/';

	// get all the matches
	preg_match_all($pattern, $content, $matches);

	// we want to return an array containing the id and a cleaned version of the id to use as a label
	$matches = $matches[1];

	$backListIfIdStartsWith = ['js-', '-'];

	// remove the matches that start with js- or -
	$matches = array_filter($matches, function ($match) use ($backListIfIdStartsWith) {
		$backList = false;

		foreach ($backListIfIdStartsWith as $backListIfIdStartsWith) {
			if (strpos($match, $backListIfIdStartsWith) === 0) {
				$backList = true;
			}
		}

		return !$backList;
	});

	// clean the matches
	$matches = array_map(function ($match) use ($page_id) {
		return [
			'title' => str_replace('-', ' ', $match),
			'permalink' => '#' . $match,

			'id' => $match,
			'label' => str_replace('-', ' ', $match),
		];
	}, $matches);

	return $matches;
}

function dump($data = false)
{
	echo '<pre>';
	var_dump($data);
	echo '</pre>';
}

function dias_get_the_permalink($post)
{
	$is_guest = $post->post_type === 'guest-author';
	$url = $is_guest
		? get_author_posts_url($post->ID, $post->post_name)
		: get_the_permalink($post->ID);

	if ($is_guest) {
		// remove cap- from the slug
		$url = str_replace('cap-', '', $url);
	}

	return $url;
}

function get_fallback_image($post_type = null)
{
	$fallback_image = get_template_directory_uri() . '/dist/images/';
	$fallback_image .= 'fallback.svg';

	return $fallback_image;
}

function add_fallback_to_hero($hero, $post)
{
	if (!has_post_thumbnail($post)) {
		$falback_image = get_fallback_image($post->post_type);
		$hero = str_replace(
			'<span aria-hidden="true" class="wp-block-cover__background has-background-dim-80 has-background-dim" style="background-color:#000"></span><div class="wp-block-cover__inner-container">',
			'<span aria-hidden="true" class="wp-block-cover__background has-background-dim-80 has-background-dim" style="background-color:#2E2D2D"></span><img class="wp-block-cover__image-background wp-image-10" alt="" src="' .
				$falback_image .
				'" data-object-fit="cover"/><div class="wp-block-cover__inner-container">',
			$hero
		);
	}

	return $hero;
}

function get_project_font_sizes()
{
	return [
		'small' => '16px',
		'body' => '18px',
		'body-large' => '21px',
		'body-xl' => '24px',
		'heading-6' => '16px',
		'heading-5' => '18px',
		'heading-4' => '21px',
		'heading-3' => '28px',
		'heading-2' => '42px',
		'heading-1' => '56px',
		'heading-1-xl' => '72px',
		'x-small' => '14px',
	];
}
function get_project_colors()
{
	// get ./src/styles/_project-colors.scss and parse it to get the colors
	$colors = [];
	$scss = file_get_contents(get_template_directory() . '/src/styles/_project-colours.scss');

	// remove any lines which start with $
	$scss = preg_replace('/^\$.*/m', '', $scss);

	// remove any comments line from the $scss
	$scss = preg_replace('/\/\/.*/', '', $scss);

	// remove ');"' from the end of the $scss
	$scss = preg_replace('/\);/', '', $scss);

	// remove $colours: (
	$scss = preg_replace('/\$colours: \(/', '', $scss);

	// remove blank lines
	$scss = preg_replace('/\n\s*\n/', "\n", $scss);

	// get the colors
	// each line now has a key and hex "key": "hex",
	$lines = explode("\n", $scss);

	foreach ($lines as $line) {
		$line = trim($line);

		// remove ' from the start and end of the line
		$line = str_replace("'", '', $line);

		if (empty($line)) {
			continue;
		}

		$line = str_replace('"', '', $line);
		$line = str_replace(',', '', $line);
		$line = explode(':', $line);

		if ($line[0] !== 'highlight' && $line[0] !== 'text') {
			$colors[$line[0]] = trim($line[1]);
		}
	}

	return $colors;
}

add_filter('gform_pre_render', 'populate_field_with_custom_post_field');

function populate_field_with_custom_post_field($form)
{
	// Get the current post ID (assuming this is the post where you want to get the custom field)
	$post_id = get_the_ID();

	// Find the specific field in the form by its ID (replace '3' with your field ID)
	foreach ($form['fields'] as &$field) {
		// if the $field inputName starts with 'custom_' then we want to populate it with a custom field
		if (strpos($field->inputName, 'custom_') === 0) {
			// Get the custom field value
			$custom_field_value = get_field(
				str_replace('custom_', '', $field->inputName),
				$post_id
			);

			if ($custom_field_value) {
				$field->defaultValue = $custom_field_value;
			}
		}
	}

	return $form;
}

function build_live_search_spans($taxonomy_name, $filter_name, $post_id)
{
	$taxonomies = get_the_terms($post_id, $taxonomy_name);

	if ($taxonomies && !isset($taxonomies->errors)) {
		build_live_search_spans_from_array($taxonomies, $filter_name);
	} else {
		// dump($taxonomy_name);
		// dump($taxonomies->errors);
	}
}

function build_live_search_spans_from_array($taxonomies, $filter_name)
{
	if ($taxonomies && !isset($taxonomies->errors)) {
		$taxonomies = array_map(function ($taxonomy) use ($filter_name) {
			return '<span data-filter="' .
				esc_attr($filter_name) .
				'">' .
				$taxonomy->slug .
				'</span>';
		}, $taxonomies);

		echo implode(', ', $taxonomies);
	} else {
		// dump($taxonomy_name);
		// dump($taxonomies->errors);
	}
}

// we want a function to convert all the dashes to em dashes
function convert_dashes($str)
{
	// get the string
	$replaced = preg_replace('/\s*-\s*/', ' — ', $str);

	// remove any double spaces
	$replaced = preg_replace('/\s+/', ' ', $replaced);
	// remove any leading or trailing spaces
}

// Prevent users from putting anything to first column
// as widget order set by user overrides everything
add_filter(
	'get_user_option_meta-box-order_dashboard',
	function ($result, $option, $user) {
		// Force custom widget to 1st column
		if (! empty($result['normal'])) {
			$result['normal'] = array('dashboard_widget_example');
		}
		return $result;
	},
	10,
	3
);

// Style dashboard widget columns
add_action(
	'admin_head',
	function () {
		if (defined('WALKTHROUGH_PLAYLIST') && WALKTHROUGH_PLAYLIST) {
			echo "<style type='text/css'>
					#dashboard-widgets .postbox-container {width: 33.333333%;}
					#wpbody-content #dashboard-widgets #postbox-container-1 {width: 100%}
			</style>";
		}
	}
);

function add_youtube_playlist_custom_widget()
{
	global $wp_meta_boxes;

	if (defined('WALKTHROUGH_PLAYLIST') && WALKTHROUGH_PLAYLIST) {
		// Move all dashboard wigets from 1st to 2nd column
		if (! empty($wp_meta_boxes['dashboard']['normal'])) {
			if (isset($wp_meta_boxes['dashboard']['side'])) {
				$wp_meta_boxes['dashboard']['side'] = array_merge_recursive(
					$wp_meta_boxes['dashboard']['side'],
					$wp_meta_boxes['dashboard']['normal']
				);
			} else {
				$wp_meta_boxes['dashboard']['side'] = $wp_meta_boxes['dashboard']['normal'];
			}
			unset($wp_meta_boxes['dashboard']['normal']);
		}

		wp_add_dashboard_widget(
			'dias_youtube_playlist_widget',
			'Site overview playlist',
			'dias_embed_youtube_playlist',
		);
	}
}

// if WALKTHROUGH_PLAYLIST is set and not false, we want to embed that youtube playlist in the dashboard as a full width box
function dias_embed_youtube_playlist()
{
	$id = WALKTHROUGH_PLAYLIST;
	$embed_url = "https://www.youtube.com/embed/videoseries?rel=0&list=" . $id;
	$link_url = "https://www.youtube.com/playlist?list=" . $id;

	echo '<div class="wrap">';
	echo '<p>This is a walkthrough of the site. It will help you get started with the site and show you how to use it.</p>';
	echo '<p>You can browse the playlist videos by clicking on the icon in the top right corner.</p>';
	echo '<p>You can also view the <a href="' . $link_url . '" target="_blank">playlist directly on Youtube</a>.</p>';
	echo '<iframe style="width: 100%; max-width: 1024px; aspect-ratio: 560 / 315; height: auto;" width="560" height="315" src="' . esc_url($embed_url) . '" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>';
	echo '</div>';
}


// embed dias_embed_youtube_playlist
add_action('wp_dashboard_setup', 'add_youtube_playlist_custom_widget');


/**
 * Define Our Themes Version
 * this good if we update something and prevent old user browser cache
 */
add_filter('w3tc_minify_urls_for_minification_to_minify_filename', 'w3tc_filename_filter', 20, 3);
function w3tc_filename_filter($minify_filename, $files, $type)
{
	$themes_version = STATIC_VERSION;

	$ver = sanitize_title(str_replace('.', '', $themes_version));

	$minify_filename = $ver . $minify_filename;

	return $minify_filename;
}
