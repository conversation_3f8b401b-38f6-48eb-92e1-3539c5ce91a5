<?php

namespace DiasTheme;

/**
 * Block Patterns Handler
 *
 * Manages registration and organization of block patterns
 */
class BlockPatterns
{
    /**
     * Initialize block patterns
     */
    public static function register(): void
    {
        // Remove core block patterns
        add_action('init', [self::class, 'removeDefaultPatterns'], 9);

        // Register custom pattern categories
        add_action('init', [self::class, 'registerPatternCategories'], 10);

        // Register custom patterns
        add_action('init', [self::class, 'registerPatterns'], 100);
    }

    /**
     * Remove default WordPress block patterns
     */
    public static function removeDefaultPatterns(): void
    {
        // Remove core WordPress patterns
        remove_theme_support('core-block-patterns');

        // Remove all default patterns from WordPress core
        add_filter('should_load_remote_block_patterns', '__return_false');

        // Remove patterns from other themes and plugins
        add_action('init', function () {
            // Check if the registry class exists
            if (!class_exists('\WP_Block_Patterns_Registry')) {
                return;
            }

            // Get all registered patterns
            $registry = \WP_Block_Patterns_Registry::get_instance();
            $patterns = $registry->get_all_registered();

            // Remove all patterns that are not from our theme
            foreach ($patterns as $pattern_name => $pattern_data) {
                // Only keep patterns that start with 'dias/' (our theme patterns)
                if (strpos($pattern_name, 'dias/') !== 0) {
                    $registry->unregister($pattern_name);
                }
            }
            unset($pattern_data); // Prevent unused variable warning
        }, 15); // Run after other patterns are registered
    }

    /**
     * Register custom pattern categories
     */
    public static function registerPatternCategories(): void
    {
        register_block_pattern_category(
            'all',
            ['label' => __('All patterns', 'dias')]
        );

        register_block_pattern_category(
            'hero',
            ['label' => __('Hero blocks', 'dias')]
        );

        register_block_pattern_category(
            'helpers',
            ['label' => __('Helper blocks', 'dias')]
        );
    }

    /**
     * Register custom block patterns
     */
    public static function registerPatterns(): void
    {
        // Check if block patterns are supported
        if (!class_exists('\WP_Block_Patterns_Registry')) {
            return;
        }

        // Register hero pattern
        register_block_pattern(
            'dias/hero-page',
            [
                'title'       => __('Hero', 'dias'),
                'description' => _x('Contains all its metadata.', 'Block pattern description', 'dias'),
                'content'     => self::getPatternContent('hero-page'),
                'categories'  => ['all', 'hero'],
            ]
        );

        // Register figure in box pattern
        register_block_pattern(
            'dias/figure-in-box',
            [
                'title'       => __('Data visualisation in white box', 'dias'),
                'description' => _x('Adds a graphic.', 'Block pattern description', 'dias'),
                'content'     => "<!-- wp:group {\"backgroundColor\":\"dias-white\",\"layout\":{\"type\":\"constrained\"}} -->\n<div class=\"wp-block-group has-dias-white-background-color has-background\"><!-- wp:heading {\"fontSize\":\"heading-5\"} -->\n<h2 class=\"wp-block-heading has-heading-5-font-size\">Figure x: title</h2>\n<!-- /wp:heading -->\n\n<!-- wp:shortcode -->\n[wpdatachart id=Y]\n<!-- /wp:shortcode -->\n\n<!-- wp:list {\"fontSize\":\"small\"} -->\n<ul class=\"wp-block-list has-small-font-size\"><!-- wp:list-item -->\n<li>description</li>\n<!-- /wp:list-item --></ul>\n<!-- /wp:list --></div>\n<!-- /wp:group -->",
                'categories'  => ['all', 'helpers'],
            ]
        );
    }

    /**
     * Get pattern content from file
     *
     * @param string $pattern_name The pattern name without extension
     * @return string The pattern content
     */
    private static function getPatternContent(string $pattern_name): string
    {
        $pattern_file = THEME_DIR . '/patterns/' . $pattern_name . '.html';

        if (file_exists($pattern_file)) {
            return file_get_contents($pattern_file);
        }

        return '';
    }
}
