<?php

namespace DiasTheme;

/**
 * Main Theme class
 *
 * Initializes and bootstraps the theme
 */
class Theme
{
    private static $instance = null;

    /**
     * Get singleton instance
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct()
    {
        $this->defineConstants();
        $this->loadDependencies();
        $this->initHooks();
    }

    /**
     * Define theme constants
     */
    private function defineConstants(): void
    {
        // Move your constants here from functions.php
        define('THEME_DIR', get_template_directory());
        define('THEME_URI', get_template_directory_uri());

        // load env.php
        require_once THEME_DIR . '/config/env.php';

        // Add other constants from your theme
        if (!defined('STATIC_VERSION')) {
            define('STATIC_VERSION', '1.0.0');
        }
    }

    /**
     * Load core dependencies
     */
    private function loadDependencies(): void
    {
        // Core classes
        require_once THEME_DIR . '/theme-core/Assets.php';
        require_once THEME_DIR . '/theme-core/Setup.php';
        require_once THEME_DIR . '/theme-core/PostTypes.php';
        require_once THEME_DIR . '/theme-core/Helpers.php';
        require_once THEME_DIR . '/theme-core/ACF.php';
        require_once THEME_DIR . '/theme-core/Admin.php';
        require_once THEME_DIR . '/theme-core/Blocks.php';
        require_once THEME_DIR . '/theme-core/BlockPatterns.php';
        require_once THEME_DIR . '/theme-core/Images.php';
    }

    /**
     * Initialize WordPress hooks
     */
    private function initHooks(): void
    {
        add_action('after_setup_theme', [$this, 'setupTheme']);
    }

    /**
     * Setup theme features and components
     */
    public function setupTheme(): void
    {
        Setup::init();
        PostTypes::register();
        Assets::register();
        ACF::init();
        Admin::init();
        Blocks::init();
        BlockPatterns::register();
        Images::init();
    }
}
