<?php
$meta = get_fields();

if (!$meta['item_to_feature'] ?? false) {
	// if in admin
	if (is_admin()) {
		// if in admin, show a message
		echo '<p class="has-body-large-font-size">Click me and select an item to feature in the sidebar.</p>';
	}
	return;
}

foreach ($meta['item_to_feature'] as $item_id) {
	$featured_item = get_post($item_id);
	$show_date = true;
	$permalink = get_permalink($item_id);
	$cta = 'Read more';
	// get the singular name of the post type
	$post_type_object = get_post_type_object($featured_item->post_type);
	$label = "Featured " . $post_type_object->labels->singular_name;

	$last_updated = get_field('last_updated', $item_id);
	$strapline = get_field('strapline', $item_id);
?>
	<article class="wp-block-columns alignwide is-layout-flex  wp-block-columns-is-layout-flex featured-item--<?php echo $featured_item->post_type; ?>">
		<div class="wp-block-column is-layout-flow wp-block-column-is-layout-flow" style="flex-basis:760px">
			<?php if ($label): ?>
				<p class="has-x-small-font-size featured-item__label"><?php echo $label ?></p>
			<?php endif; ?>

			<h2 class="wp-block-heading"><?php echo $featured_item->post_title ?></h2>
			<?php if ($strapline): ?>
				<p class="has-body-large-font-size"><?php echo $strapline; ?></p>
			<?php endif; ?>

			<p class="has-x-small-font-size"></p>
			<?php if ($show_date): ?>
				<p class="has-small-copy-font-size featured-date">
					Published: <?php echo esc_html(get_the_date('j M Y')); ?>
					<?php
					if ($last_updated): ?>
						<br>Last updated: <?php echo esc_html($last_updated); ?>
					<?php endif; ?>
				</p>
			<?php endif; ?>

			<div class="wp-block-buttons is-layout-flex wp-block-buttons-is-layout-flex">
				<div class="wp-block-button"><a href="<?php echo $permalink; ?>" class="wp-block-button__link has-dias-white-background-color has-background wp-element-button"><?php echo $cta ?><span class="sr-only">about <?php echo $featured_item->post_title; ?></span></a></div>
			</div>
		</div>
	</article>
<?php
}
