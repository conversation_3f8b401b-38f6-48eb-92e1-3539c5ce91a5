<?php
acf_add_local_field_group(array(
  'key' => 'group_67600ec4a1cd9',
  'title' => 'BLOCK: Large full-width card',
  'fields' => array(
    array(
      'key' => 'field_67600ec4cbc6d',
      'label' => 'Item to feature',
      'name' => 'item_to_feature',
      'aria-label' => '',
      'type' => 'relationship',
      'instructions' => 'Choose 1 item only',
      'required' => 1,
      'conditional_logic' => 0,
      'wrapper' => array(
        'width' => '',
        'class' => '',
        'id' => '',
      ),
      'post_type' => array(
        0 => 'bulletin',
        1 => 'snapshot',
        2 => 'issue-paper',
      ),
      'post_status' => '',
      'taxonomy' => '',
      'filters' => array(
        0 => 'search',
        1 => 'post_type',
      ),
      'return_format' => 'id',
      'min' => 1,
      'max' => 1,
      'allow_in_bindings' => 0,
      'elements' => '',
      'bidirectional' => 0,
      'bidirectional_target' => array(),
    ),
  ),
  'location' => array(
    array(
      array(
        'param' => 'block',
        'operator' => '==',
        'value' => 'dias/featured-card',
      ),
    ),
  ),
  'menu_order' => 0,
  'position' => 'normal',
  'style' => 'default',
  'label_placement' => 'top',
  'instruction_placement' => 'label',
  'hide_on_screen' => '',
  'active' => true,
  'description' => '',
  'show_in_rest' => 0,
));
