<?php
// current page id
$page_id = get_the_ID();
$download_links = get_field('files', $page_id);
$meta = get_fields();
$style = $meta['subnav_style'] ?? 'default';

$subLinks = getPageAnchors($page_id);
?>
<?php if (empty($subLinks) && is_admin()) : ?>
	<nav class="in-page-sub-nav in-page-sub-nav--<?php echo esc_attr($style) ?> js-sub-nav">
		<ul>
			<li><a>Subnav: Add anchor links to items in this page for them to appear here. Once changes are saved and the page is reloaded.</a></li>
		</ul>
	</nav>
<?php elseif (!empty($subLinks) || !empty($download_links)) : ?>
	<nav class="in-page-sub-nav in-page-sub-nav--<?php echo esc_attr($style) ?> js-sub-nav">
		<ul>
			<?php if (!empty($subLinks)) : ?>
				<?php foreach ($subLinks as $link) : ?>
					<li>
						<a href="<?php echo $link['permalink']; ?>" class="">
							<?php echo $link['title']; ?>
						</a>
					</li>
				<?php endforeach; ?>
			<?php endif; ?>

			<?php if (!empty($download_links)) : ?>
				<li>
					<div class="wp-block-buttons">
						<?php foreach ($download_links as $download) :
							$link = $download['link_and_cta'];
							if ($link): ?>
								<a href="<?php echo esc_url($link['url']); ?>" class="wp-block-button__link">
									<?php echo esc_html($link['title']); ?>
								</a>
							<?php endif; ?>
						<?php endforeach; ?>
					</div>
				</li>
			<?php endif; ?>
		</ul>
	<?php endif; ?>
	</nav>