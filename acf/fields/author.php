<?php
acf_add_local_field_group([
  'key' => 'group_67a243ffb297e',
  'title' => 'Author meta',
  'fields' => [
    [
      'key' => 'field_67a47cfc0884c',
      'label' => 'Avatar',
      'name' => 'avatar',
      'aria-label' => '',
      'type' => 'image',
      'instructions' => '',
      'required' => 0,
      'conditional_logic' => 0,
      'wrapper' => [
        'width' => '',
        'class' => '',
        'id' => '',
      ],
      'relevanssi_exclude' => 0,
      'return_format' => 'id',
      'library' => 'all',
      'min_width' => '',
      'min_height' => '',
      'min_size' => '',
      'max_width' => '',
      'max_height' => '',
      'max_size' => '',
      'mime_types' => '',
      'allow_in_bindings' => 0,
      'preview_size' => 'medium',
    ],
    // Add other fields as needed
  ],
  'location' => [
    [
      [
        'param' => 'user_form',
        'operator' => '==',
        'value' => 'all',
      ],
    ],
  ],
]);
