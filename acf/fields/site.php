<?php
acf_add_local_field_group(array(
  'key' => 'group_67a9e6fe12290',
  'title' => 'Related content',
  'fields' => array(
    array(
      'key' => 'field_67a9e6fe7f23c',
      'label' => 'Related items',
      'name' => 'related_items',
      'aria-label' => '',
      'type' => 'relationship',
      'instructions' => '',
      'required' => 0,
      'conditional_logic' => 0,
      'wrapper' => array(
        'width' => '',
        'class' => '',
        'id' => '',
      ),
      'relevanssi_exclude' => 0,
      'post_type' => array(
        0 => 'post',
        1 => 'event',
        2 => 'service',
        3 => 'page',
      ),
      'post_status' => '',
      'taxonomy' => '',
      'filters' => array(
        0 => 'search',
        1 => 'post_type',
      ),
      'return_format' => 'id',
      'min' => '',
      'max' => '',
      'allow_in_bindings' => 1,
      'elements' => '',
      'bidirectional' => 0,
      'bidirectional_target' => array(),
    ),
  ),
  'location' => array(
    array(
      array(
        'param' => 'post_type',
        'operator' => '==',
        'value' => 'post',
      ),
    ),
    array(
      array(
        'param' => 'post_type',
        'operator' => '==',
        'value' => 'page',
      ),
    ),
    array(
      array(
        'param' => 'post_type',
        'operator' => '==',
        'value' => 'event',
      ),
    ),
    array(
      array(
        'param' => 'post_type',
        'operator' => '==',
        'value' => 'service',
      ),
    ),
  ),
  'menu_order' => 20,
  'position' => 'side',
  'style' => 'default',
  'label_placement' => 'top',
  'instruction_placement' => 'label',
  'hide_on_screen' => '',
  'active' => true,
  'description' => '',
  'show_in_rest' => 0,
));

acf_add_local_field_group(array(
  'key' => 'group_67adce6cda9c9',
  'title' => 'Sticky',
  'fields' => array(
    array(
      'key' => 'field_67adce6d7d21d',
      'label' => 'Pin this post to the top of the listing',
      'name' => 'sticky',
      'aria-label' => '',
      'type' => 'true_false',
      'instructions' => '',
      'required' => 0,
      'conditional_logic' => 0,
      'wrapper' => array(
        'width' => '',
        'class' => '',
        'id' => '',
      ),
      'relevanssi_exclude' => 1,
      'message' => '',
      'default_value' => 0,
      'allow_in_bindings' => 0,
      'ui_on_text' => '',
      'ui_off_text' => '',
      'ui' => 1,
    ),
  ),
  'location' => array(
    array(
      array(
        'param' => 'post_type',
        'operator' => '==',
        'value' => 'post',
      ),
    ),
    array(
      array(
        'param' => 'post_type',
        'operator' => '==',
        'value' => 'event',
      ),
    ),
    array(
      array(
        'param' => 'post_type',
        'operator' => '==',
        'value' => 'service',
      ),
    ),
  ),
  'menu_order' => -20,
  'position' => 'side',
  'style' => 'default',
  'label_placement' => 'top',
  'instruction_placement' => 'label',
  'hide_on_screen' => '',
  'active' => true,
  'description' => '',
  'show_in_rest' => 0,
));

acf_add_local_field_group(array(
  'key' => 'group_67add62d71aef',
  'title' => 'Support archive options',
  'fields' => array(
    array(
      'key' => 'field_67add62db7cd6',
      'label' => 'Weigh all sticky items equally',
      'name' => 'weigh_all_sticky_items_equally',
      'aria-label' => '',
      'type' => 'true_false',
      'instructions' => 'This will make sure the first card is the same width as the others, even if it is sticky.',
      'required' => 0,
      'conditional_logic' => 0,
      'wrapper' => array(
        'width' => '',
        'class' => '',
        'id' => '',
      ),
      'relevanssi_exclude' => 0,
      'message' => '',
      'default_value' => 0,
      'allow_in_bindings' => 0,
      'ui_on_text' => '',
      'ui_off_text' => '',
      'ui' => 1,
    ),
  ),
  'location' => array(
    array(
      array(
        'param' => 'page',
        'operator' => '==',
        'value' => '7330',
      ),
    ),
  ),
  'menu_order' => -10,
  'position' => 'side',
  'style' => 'default',
  'label_placement' => 'top',
  'instruction_placement' => 'label',
  'hide_on_screen' => '',
  'active' => true,
  'description' => '',
  'show_in_rest' => 0,
));
